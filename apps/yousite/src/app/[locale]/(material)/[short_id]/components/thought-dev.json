{"id": "01972488-49a9-71c4-bcb8-bbf939e4971a", "created_at": "2025-05-31T04:10:01.001Z", "updated_at": "2025-08-03T06:03:27.022Z", "title": "Mobx   前端数据流方案详解", "title_type": "manual", "content": {"raw": "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", "plain": "\n目前我们的项目是使用 React 框架\n\n# Youmind 前端 Mobx 前端数据流方案\n\n## 1. Mobx 简介\n\nMobx 是一个简单、可扩展的状态管理库，它通过透明的函数响应式编程使得状态管理变得简单和可扩展。Mobx 的核心理念是：\n\n- **任何可以从应用状态派生的东西都应该自动进行派生**\n\n- **以最小的仪式感和最大的表达力创建可观察状态**\n\n- **以直观、高效的方式响应状态变化**\n\n与 Redux 等其他状态管理库相比，Mobx 采用了更加直观的响应式编程模型，减少了模板代码，提高了开发效率。\n\n## 2. Mobx 核心概念\n\n### 2.1 Observable State (可观察状态)\n\n可观察状态是 Mobx 的核心。通过使用 `observable` 装饰器或函数，我们可以将对象、数组和基本类型转换为可被观察的数据。\n\n```typescript\nimport { makeObservable, observable } from 'mobx';\n\nclass Store {\n  @observable count = 0;\n  @observable users = [];\n  @observable user = { name: 'John', age: 30 };\n  \n  constructor() {\n    makeObservable(this);\n  }\n}\n\n// 或者使用 makeAutoObservable\nclass StoreAuto {\n  count = 0;\n  users = [];\n  user = { name: 'John', age: 30 };\n  \n  constructor() {\n    makeAutoObservable(this);\n  }\n}\n```\n\n### 2.2 Actions (动作)\n\nActions 是修改状态的唯一方法。通过将方法标记为 action，Mobx 可以自动批量处理状态更新，提高性能并保证状态一致性。\n\n```typescript\nimport { makeObservable, observable, action } from 'mobx';\n\nclass Counter {\n  @observable count = 0;\n  \n  constructor() {\n    makeObservable(this);\n  }\n  \n  @action\n  increment() {\n    this.count++;\n  }\n  \n  @action\n  decrement() {\n    this.count--;\n  }\n  \n  @action\n  reset() {\n    this.count = 0;\n  }\n}\n```\n\n### 2.3 Computed Values (计算值)\n\nComputed values 是从可观察状态派生出的值。它们会自动缓存，只有当依赖的可观察状态发生变化时才会重新计算。\n\n```typescript\nimport { makeObservable, observable, computed } from 'mobx';\n\nclass TodoList {\n  @observable todos = [];\n  \n  constructor() {\n    makeObservable(this);\n  }\n  \n  @computed\n  get completedTodosCount() {\n    return this.todos.filter(todo => todo.completed).length;\n  }\n  \n  @computed\n  get remainingTodosCount() {\n    return this.todos.length - this.completedTodosCount;\n  }\n}\n```\n\n### 2.4 Reactions (反应)\n\nReactions 是对状态变化的响应。Mobx 提供了三种主要的 reaction 类型：\n\n- **autorun**: 自动追踪并响应函数中使用的可观察状态\n\n- **reaction**: 更细粒度地控制追踪哪些可观察状态\n\n- **when**: 当条件满足时执行一次回调并自动清理\n\n```typescript\nimport { autorun, reaction, when } from 'mobx';\nimport { todoStore } from './stores';\n\n// autorun 示例\nconst disposer1 = autorun(() => {\n  console.log(`Completed todos: ${todoStore.completedTodosCount}`);\n});\n\n// reaction 示例\nconst disposer2 = reaction(\n  () => todoStore.completedTodosCount,\n  completedCount => {\n    console.log(`Completed count changed to: ${completedCount}`);\n  }\n);\n\n// when 示例\nconst disposer3 = when(\n  () => todoStore.completedTodosCount > 5,\n  () => {\n    console.log('You have completed more than 5 todos!');\n  }\n);\n\n// 不再需要时清理\ndisposer1();\ndisposer2();\ndisposer3();\n```\n\n## 3. Mobx 与 React 集成\n\n### 3.1 observer 组件\n\n通过 `mobx-react` 或 `mobx-react-lite` 提供的 `observer` 高阶组件，React 组件可以响应式地渲染 Mobx 状态的变化。\n\n```typescript\nimport React from 'react';\nimport { observer } from 'mobx-react-lite';\nimport { todoStore } from '../stores';\n\nconst TodoList = observer(() => {\n  return (\n    \n```\n\n## `Todo List`\n\n`Completed: {todoStore.completedTodosCount}`\n\n`Remaining: {todoStore.remainingTodosCount}`\n\n- `         {todoStore.todos.map(todo => (           `\n\n- `             <input type=\"checkbox\" checked=\"{todo.completed}\" onchange=\"{()\" ==\"\"> todo.toggle()}             />             {todo.title}           `\n\n- `         ))}       `\n\n`   ); });  export default TodoList;`\n\n### 3.2 使用 Hooks 访问 Store\n\n在函数组件中，我们可以使用 React Context 和 Hooks 来访问 Mobx stores。\n\n```typescript\n// stores/index.tsx\nimport React, { createContext, useContext } from 'react';\nimport { TodoStore } from './todoStore';\nimport { UserStore } from './userStore';\n\nclass RootStore {\n  todoStore: TodoStore;\n  userStore: UserStore;\n  \n  constructor() {\n    this.todoStore = new TodoStore(this);\n    this.userStore = new UserStore(this);\n  }\n}\n\nconst StoreContext = createContext(null);\n\nexport const StoreProvider: React.FC = ({ children }) => {\n  const store = new RootStore();\n  return (\n    \n      {children}\n    \n  );\n};\n\nexport const useStore = () => {\n  const store = useContext(StoreContext);\n  if (!store) {\n    throw new Error('useStore must be used within a StoreProvider');\n  }\n  return store;\n};\n\n// App.tsx\nimport React from 'react';\nimport { StoreProvider } from './stores';\nimport TodoList from './components/TodoList';\n\nconst App: React.FC = () => {\n  return (\n    \n      \n    \n  );\n};\n\n// TodoList.tsx\nimport React from 'react';\nimport { observer } from 'mobx-react-lite';\nimport { useStore } from '../stores';\n\nconst TodoList = observer(() => {\n  const { todoStore } = useStore();\n  \n  return (\n    \n```\n\n## `Todo List`\n\n`Completed: {todoStore.completedTodosCount}`\n\n`       {/* 其余代码... */}     `\n\n`   ); }); `\n\n## 4. Mobx 最佳实践\n\n### 4.1 Store 设计原则\n\n1. **领域划分**: 按照业务领域划分 Store，而不是按照技术功能\n\n2. **单一职责**: 每个 Store 应该只负责一个领域的状态管理\n\n3. **组合优于继承**: 使用 Store 之间的引用来组合功能，而不是继承\n\n```typescript\n// 良好的 Store 设计示例\nclass UserStore {\n  @observable users = [];\n  @observable currentUser = null;\n  \n  constructor(rootStore) {\n    this.rootStore = rootStore;\n    makeObservable(this);\n  }\n  \n  @action\n  login(username, password) {\n    // 实现登录逻辑\n  }\n}\n\nclass TodoStore {\n  @observable todos = [];\n  \n  constructor(rootStore) {\n    this.rootStore = rootStore;\n    makeObservable(this);\n  }\n  \n  @action\n  addTodo(title) {\n    const todo = {\n      id: Date.now(),\n      title,\n      completed: false,\n      // 可以引用用户信息\n      createdBy: this.rootStore.userStore.currentUser?.id\n    };\n    this.todos.push(todo);\n  }\n}\n```\n\n### 4.2 异步操作处理\n\n在 Mobx 中处理异步操作时，需要确保状态更新在 action 中进行：\n\n```typescript\nimport { makeObservable, observable, action, runInAction } from 'mobx';\n\nclass TodoStore {\n  @observable todos = [];\n  @observable loading = false;\n  @observable error = null;\n  \n  constructor() {\n    makeObservable(this);\n  }\n  \n  @action\n  async fetchTodos() {\n    this.loading = true;\n    this.error = null;\n    \n    try {\n      const response = await fetch('/api/todos');\n      const todos = await response.json();\n      \n      // 使用 runInAction 包装异步操作后的状态更新\n      runInAction(() => {\n        this.todos = todos;\n        this.loading = false;\n      });\n    } catch (error) {\n      runInAction(() => {\n        this.error = error.message;\n        this.loading = false;\n      });\n    }\n  }\n}\n```\n\n### 4.3 性能优化\n\n1. **使用 computed 值**：避免在渲染时进行复杂计算\n\n2. **合理拆分 observer 组件**：避免大组件因小状态变化而完全重渲染\n\n3. **使用 reaction 代替 autorun**：更精确地控制响应范围\n\n```typescript\n// 优化前\nconst TodoApp = observer(() => {\n  const { todoStore } = useStore();\n  \n  return (\n    \n```\n\n# `Todo App`\n\n`Total: {todoStore.todos.length}`\n\n`Completed: {todoStore.todos.filter(t => t.completed).length}`\n\n`   ); });  // 优化后 const TodoStats = observer(() => {   const { todoStore } = useStore();      return (     `\n\n`Total: {todoStore.todos.length}`\n\n`Completed: {todoStore.completedTodosCount}`\n\n`   ); });  const TodoApp = () => {   const { todoStore } = useStore();      return (     `\n\n# `Todo App`\n\n`   ); };`\n\n## 5. Mobx 与其他状态管理库的比较\n\n### 5.1 Mobx vs Redux\n\n| 特性 | Mobx | Redux |\n| --- | --- | --- |\n| 编程范式 | 响应式 | 函数式 |\n| 状态结构 | 多个独立 store，结构灵活 | 单一 store，结构严格 |\n| 不可变性 | 内部处理，对开发者透明 | 需要手动确保 |\n| 模板代码 | 较少 | 较多 |\n| 学习曲线 | 较平缓 | 较陡峭 |\n| 调试能力 | 一般 | 强大 (时间旅行) |\n| 适用场景 | 中小型应用，快速开发 | 大型应用，严格的状态管理 |\n\n### 5.2 何时选择 Mobx\n\nMobx 特别适合以下场景：\n\n1. 需要快速开发的项目\n\n2. 团队偏好面向对象编程\n\n3. 应用状态较为复杂但不需要严格的状态追踪\n\n4. 希望减少模板代码\n\n5. 需要更高的性能（在某些场景下）\n\n## 6. 实际项目中的 Mobx 应用示例\n\n### 6.1 用户认证模块\n\n```typescript\n// stores/authStore.ts\nimport { makeAutoObservable, runInAction } from 'mobx';\nimport { AuthService } from '../services/authService';\n\ninterface User {\n  id: string;\n  username: string;\n  email: string;\n}\n\nclass AuthStore {\n  user: User | null = null;\n  token: string | null = null;\n  loading = false;\n  error: string | null = null;\n  \n  constructor(private authService: AuthService) {\n    makeAutoObservable(this);\n    this.initFromStorage();\n  }\n  \n  private initFromStorage() {\n    const token = localStorage.getItem('token');\n    const userJson = localStorage.getItem('user');\n    \n    if (token && userJson) {\n      this.token = token;\n      this.user = JSON.parse(userJson);\n    }\n  }\n  \n  get isAuthenticated() {\n    return !!this.token && !!this.user;\n  }\n  \n  async login(email: string, password: string) {\n    this.loading = true;\n    this.error = null;\n    \n    try {\n      const { user, token } = await this.authService.login(email, password);\n      \n      runInAction(() => {\n        this.user = user;\n        this.token = token;\n        this.loading = false;\n      });\n      \n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n    } catch (error) {\n      runInAction(() => {\n        this.error = error.message;\n        this.loading = false;\n      });\n    }\n  }\n  \n  logout() {\n    this.user = null;\n    this.token = null;\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  }\n}\n\nexport default AuthStore;\n```\n\n### 6.2 数据列表与分页\n\n```typescript\n// stores/productStore.ts\nimport { makeAutoObservable, runInAction } from 'mobx';\nimport { ProductService } from '../services/productService';\n\ninterface Product {\n  id: string;\n  name: string;\n  price: number;\n  description: string;\n}\n\ninterface PaginationParams {\n  page: number;\n  limit: number;\n  total: number;\n}\n\nclass ProductStore {\n  products: Product[] = [];\n  pagination: PaginationParams = {\n    page: 1,\n    limit: 10,\n    total: 0\n  };\n  loading = false;\n  error: string | null = null;\n  searchQuery = '';\n  \n  constructor(private productService: ProductService) {\n    makeAutoObservable(this);\n  }\n  \n  get totalPages() {\n    return Math.ceil(this.pagination.total / this.pagination.limit);\n  }\n  \n  async fetchProducts() {\n    this.loading = true;\n    this.error = null;\n    \n    try {\n      const { data, total } = await this.productService.getProducts({\n        page: this.pagination.page,\n        limit: this.pagination.limit,\n        query: this.searchQuery\n      });\n      \n      runInAction(() => {\n        this.products = data;\n        this.pagination.total = total;\n        this.loading = false;\n      });\n    } catch (error) {\n      runInAction(() => {\n        this.error = error.message;\n        this.loading = false;\n      });\n    }\n  }\n  \n  setPage(page: number) {\n    this.pagination.page = page;\n    this.fetchProducts();\n  }\n  \n  setSearchQuery(query: string) {\n    this.searchQuery = query;\n    this.pagination.page = 1; // 重置到第一页\n    this.fetchProducts();\n  }\n}\n\nexport default ProductStore;\n```\n\n## 7. 结论\n\nMobx 是一个强大而灵活的状态管理库，它通过响应式编程模型简化了前端应用的状态管理。相比于其他状态管理解决方案，Mobx 提供了更少的模板代码和更直观的编程模型，使开发者能够专注于业务逻辑而非状态管理的细节。\n\n通过合理设计 Store 结构、遵循最佳实践，Mobx 可以帮助我们构建高性能、可维护的前端应用。无论是小型项目还是中大型应用，Mobx 都能提供适合的状态管理解决方案。"}, "board_ids": ["0194463b-fc37-74c2-bea1-8f097f0b514c"], "board_item": {"id": "01972488-49e3-785e-960b-c4b30beb3a01", "created_at": "2025-05-31T04:10:01.060Z", "updated_at": "2025-05-31T04:10:01.060Z", "board_id": "0194463b-fc37-74c2-bea1-8f097f0b514c", "thought_id": "01972488-49a9-71c4-bcb8-bbf939e4971a", "rank": "XsEh"}, "visibility": "public"}