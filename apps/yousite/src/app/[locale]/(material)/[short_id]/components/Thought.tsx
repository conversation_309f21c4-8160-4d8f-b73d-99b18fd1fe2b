'use client';

import { ThoughtVO, TitleTypeEnum, ThoughtStatusEnum } from '@repo/common/types/thought/types';
import { ThoughtPreview } from '@repo/ui-business-editor/thought/thought-preview';
import SharedHeader from './header';
import thoughtDevData from './thought-dev.json';

export function Thought({ thought }: { thought: ThoughtVO }) {
  // 在开发环境中使用 mock 数据
  const isDevelopment = process.env.NODE_ENV === 'development';

  // 将 JSON 数据转换为符合 ThoughtVO 类型的对象
  const mockThought: ThoughtVO = {
    id: thoughtDevData.id,
    creator_id: 'mock-creator-id', // mock 数据中没有，添加一个默认值
    space_id: 'mock-space-id', // mock 数据中没有，添加一个默认值
    created_at: new Date(thoughtDevData.created_at),
    updated_at: new Date(thoughtDevData.updated_at),
    title: thoughtDevData.title,
    title_type: thoughtDevData.title_type as TitleTypeEnum,
    content: thoughtDevData.content,
    status: ThoughtStatusEnum.PUBLISHED, // mock 数据中没有，添加一个默认值
    visibility: 'public', // mock 数据中没有，添加一个默认值
    language: 'zh-CN', // mock 数据中没有，添加一个默认值
    tags: [], // mock 数据中没有，添加一个默认值
  };

  // 在开发环境中使用 mock 数据，否则使用传入的 thought
  const displayThought = isDevelopment ? mockThought : thought;

  return (
    <>
      <SharedHeader />
      <main className="mx-auto max-w-[800px] px-6 py-5">
        {/* [FIXME] 动动看这里 */}
        {/* todo 等 muke 搞完 thought 类型之后再进行修改 */}
        <ThoughtPreview clearDiff showNavigation thought={displayThought as any} />
      </main>
    </>
  );
}
