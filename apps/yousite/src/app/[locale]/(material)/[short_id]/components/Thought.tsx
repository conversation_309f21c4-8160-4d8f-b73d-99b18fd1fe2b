'use client';

import { ThoughtVO } from '@repo/common/types/thought/types';
import { ThoughtPreview } from '@repo/ui-business-editor/thought/thought-preview';
import SharedHeader from './header';

export function Thought({ thought }: { thought: ThoughtVO }) {
  return (
    <>
      <SharedHeader />
      <main className="mx-auto max-w-[800px] px-6 py-5">
        {/* [FIXME] 动动看这里 */}
        {/* todo 等 muke 搞完 thought 类型之后再进行修改 */}
        <ThoughtPreview clearDiff showNavigation thought={thought as any} />
      </main>
    </>
  );
}
