/**
 * NestJS 全局异常过滤器
 * 使用结构化日志和 New Relic 跟踪关联进行错误处理
 * 升级为支持 OpenTelemetry 的错误日志记录
 */

import { ArgumentsHost, Catch, ExceptionFilter, HttpException, Injectable } from '@nestjs/common';
import { trace } from '@opentelemetry/api';
import { Response } from 'express';
import { PinoLogger } from 'nestjs-pino';
import { ZodError } from 'zod';
import { fromError } from 'zod-validation-error';

import { InvalidArguments, RestError, UnknownError } from '../errors';
import { YouapiClsService } from '../services/cls.service';

@Injectable()
@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(
    private readonly logger: PinoLogger,
    private readonly clsService: YouapiClsService,
  ) {
    this.logger.setContext(HttpExceptionFilter.name);
  }

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const error = this.convertError(exception);
    const span = trace.getActiveSpan();

    // 添加错误属性到 OpenTelemetry span
    span?.setAttributes({
      error: true,
      'error.name': error.name,
      'error.message': error.message,
      'error.status': error.status,
      'http.status_code': error.status,
    });

    const errorMessage = `${error.status} ${error.name}: ${error.message}`;
    const userId = this.clsService.getUserId();
    const spaceId = this.clsService.getSpaceId();
    const context = [userId ? `user_id: ${userId}` : null, spaceId ? `space_id: ${spaceId}` : null]
      .filter(Boolean)
      .join(' | ');

    const logMessage = context ? `${errorMessage} | ${context}` : errorMessage;

    // 记录错误日志 - 简单明了
    if (error.status >= 400 && error.status < 500) {
      this.logger.warn(logMessage);
    } else if (error.status >= 500) {
      this.logger.error(logMessage);
    }

    // 返回错误响应
    const errorResponse = error.json(error.message || 'An error occurred');

    response.status(error.status).json(errorResponse);
  }

  private convertError(error: unknown): RestError<unknown> {
    if (error instanceof RestError) {
      return error;
    } else if (error instanceof HttpException) {
      // 处理 NestJS 内置的 HttpException
      const status = error.getStatus();
      const response = error.getResponse();
      const message =
        typeof response === 'string'
          ? response
          : (response as { message?: string })?.message || error.message;

      return new (class extends RestError<unknown> {
        name = (error as Error).constructor.name;
        status = status;
      })(message);
    } else if (error instanceof ZodError) {
      const validationError = fromError(error);
      validationError.stack = error.stack;

      // 结构化记录验证错误
      this.logger.warn({
        message: 'Zod validation error',
        'error.type': 'ZodValidationException',
        'error.message': validationError.message,
        'validation.issues': error.issues,
        'validation.input': error.format(),
        'trace.id': trace.getActiveSpan()?.spanContext().traceId,
        'span.id': trace.getActiveSpan()?.spanContext().spanId,
      });

      return new InvalidArguments(validationError.message);
    } else {
      return new UnknownError(error instanceof Error ? error.stack : 'Unknown error');
    }
  }
}
