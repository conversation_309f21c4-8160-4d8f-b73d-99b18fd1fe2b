/**
 * 通用模块
 * 导出所有通用的过滤器、守卫、中间件、拦截器和服务
 */

import { Global, Module } from '@nestjs/common';
import { APP_FILTER, APP_GUARD } from '@nestjs/core';

import { DaoModule } from '@/dao';
import { DatabaseService } from '@/shared/db/database.service';
import { SupabaseService } from '@/shared/supabase/supabase.service';
import { HttpExceptionFilter } from './filters/http-exception.filter';
import { AuthGuard } from './guards/auth.guard';
import { AuthService } from './services/auth.service';
import { YouapiClsService } from './services/cls.service';
import { AppConfigService } from './services/config.service';
import { LangfuseTraceService } from './services/langfuse-trace.service';

@Global()
@Module({
  imports: [DaoModule],
  providers: [
    AuthService,
    AppConfigService,
    // 全局异常过滤器
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    // 全局认证守卫
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    // 全局 Langfuse 跟踪刷新拦截器
    // @FIXME @桑绿 后续看看怎么让这个 interceptor 只针对 chat/ai 相关的请求生效
    // {
    //   provide: APP_INTERCEPTOR,
    //   useClass: TraceFlushInterceptor,
    // },
    // CLS 服务
    YouapiClsService,
    // Langfuse 跟踪服务
    LangfuseTraceService,
    // Supabase 服务
    SupabaseService,
    // 数据库服务
    DatabaseService,
  ],
  exports: [
    YouapiClsService,
    AuthService,
    AppConfigService,
    LangfuseTraceService,
    SupabaseService,
    DatabaseService,
  ],
})
export class CommonModule {}
