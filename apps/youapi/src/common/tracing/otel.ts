/**
 * OpenTelemetry 相关配置和工具
 * 从 youapp/src/lib/common/otel.ts 迁移而来
 * 适配到 NestJS 框架
 */

import * as otel from '@opentelemetry/api';
import { TRACE_PARENT_HEADER, W3CTraceContextPropagator } from '@opentelemetry/core';
import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';

export const REQUEST_STORE_KEY = otel.createContextKey('request-store');

export const FAKE_TRACE_PARENT_KEY = otel.createContextKey('fake-trace-parent');

export interface RichSpan extends otel.Span {
  resource: {
    _attributes: {
      [ATTR_SERVICE_NAME]: string;
    };
  };
}

export enum TracerEnum {
  YOUAPI = 'youapi',
  YOUWEB = 'youweb',
}

export function getServiceName(): string {
  const env =
    process.env.NODE_ENV === 'development' ? 'local' : (process.env.YOUMIND_ENV ?? 'local');
  const baseService = TracerEnum.YOUAPI;

  if (env === 'local') {
    return `${baseService}-local`;
  }
  if (env === 'preview') {
    return `${baseService}-preview`;
  }
  return baseService; // production
}

export enum OTELKeyEnum {
  /*
   * semantic-conventions 包里似乎找不到标准的 traceId 和 userId 命名
   * 先按 OTEL 规范来定，同时要能够被 New Relic 识别：
   * https://opentelemetry.io/docs/specs/semconv/general/attribute-naming/
   */
  TRACE_ID = 'trace.id',
  USER_ID = 'user.id',
  SPACE_ID = 'space.id',
}

/**
 * InitRequestStorePropagator - 服务端请求存储传播器
 * 从 youapp/src/lib/server/instrumentation/index.ts 迁移而来
 * 在请求开始时初始化 RequestContext Store，以便在整个请求生命周期中共享数据
 */
export class InitRequestStorePropagator extends W3CTraceContextPropagator {
  extract(context: otel.Context, carrier: unknown, getter: otel.TextMapGetter): otel.Context {
    // 在一开始就初始化好 RequestContext Store，以便在后续整个请求中都可以共享
    const ctx = super.extract(context, carrier, getter);
    return ctx.setValue(REQUEST_STORE_KEY, {});
  }
}

/**
 * FakeTraceParentPropagator - 伪造追踪父级传播器
 * 从 youapp/src/lib/edge/instrumentation.ts 迁移而来
 * 当没有 trace parent header 时，创建伪造的 trace parent spans
 * 主要用于边缘运行时/中间件追踪
 */
export class FakeTraceParentPropagator extends W3CTraceContextPropagator {
  extract(context: otel.Context, carrier: unknown, getter: otel.TextMapGetter): otel.Context {
    if (getter.get(carrier, TRACE_PARENT_HEADER)) {
      return super.extract(context, carrier, getter);
    } else {
      const tracer = otel.trace.getTracer(TracerEnum.YOUWEB);
      const fakeParentSpan = tracer.startSpan('fake-parent-span');
      const fakeParentSpanContext = fakeParentSpan.spanContext();
      fakeParentSpanContext.isRemote = true;
      const ctx = otel.trace.setSpanContext(context, fakeParentSpanContext);
      const fakeTraceparent = `00-${fakeParentSpanContext.traceId}-${fakeParentSpanContext.spanId}-01`;
      return ctx.setValue(FAKE_TRACE_PARENT_KEY, fakeTraceparent);
    }
  }
}
