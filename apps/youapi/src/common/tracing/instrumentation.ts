/**
 * Tracing 初始化
 * 从 youapp/src/lib/server/instrumentation/index.ts 和 youapp/src/lib/edge/instrumentation.ts 迁移而来
 * 适配到 NestJS 框架，包含两个自定义传播器：
 * 1. InitRequestStorePropagator - 初始化请求存储上下文
 * 2. FakeTraceParentPropagator - 创建虚假父级追踪span（当缺少trace parent header时）
 */

import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { CompositePropagator, W3CBaggagePropagator } from '@opentelemetry/core';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { PinoInstrumentation } from '@opentelemetry/instrumentation-pino';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { FakeTraceParentPropagator, getServiceName, InitRequestStorePropagator } from './otel';

// Uses https://github.com/pragmaticivan/nestjs-otel
// Initialize the SDK with comprehensive configuration
const sdk = new NodeSDK({
  serviceName: getServiceName(),
  instrumentations: [
    getNodeAutoInstrumentations({
      // Disable some instrumentations that might cause issues in development
      '@opentelemetry/instrumentation-fs': {
        enabled: false,
      },
    }),
    new PinoInstrumentation(),
  ],
  // Set up composite propagators with both custom propagators
  textMapPropagator: new CompositePropagator({
    propagators: [
      new InitRequestStorePropagator(),
      new FakeTraceParentPropagator(),
      new W3CBaggagePropagator(),
    ],
  }),
  traceExporter: new OTLPTraceExporter({
    url: 'https://otlp.nr-data.net/v1/traces',
    headers: {
      'api-key': process.env.NEW_RELIC_LICENSE_KEY || '',
    },
  }),
});

export default sdk;
