import './instrumentation';
import 'source-map-support/register';

import { join } from 'node:path';
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import cookieParser from 'cookie-parser';
import { Logger } from 'nestjs-pino';
import { AppModule } from './app.module';
import {
  addCamelCaseHeaderToOperations,
  transformSwaggerDocumentToSnakeCase,
} from './shared/swagger-transform.util';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bufferLogs: true,
    snapshot: true,
    rawBody: true, // 启用原始请求体保存
    abortOnError: false,
    logger: false, // 禁用默认 logger
  });
  const port = process.env.PORT ?? '4000';

  // Enable cookie parser
  app.use(cookieParser());

  app.setBaseViewsDir(join(__dirname, '..', 'views'));
  app.setViewEngine('hbs');

  // 统一使用 pino logger
  app.useLogger(app.get(Logger));

  // Enable CORS
  app.enableCors({
    origin: true,
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true, // 只保留DTO中定义的属性
      enableDebugMessages: true,
    }),
  );

  // Swagger setup
  const config = new DocumentBuilder().setTitle('YouAPI').setVersion('1.0').addTag('bff').build();

  // Generate the original camelCase document
  const document = SwaggerModule.createDocument(app, config);

  const camelCaseDocument = addCamelCaseHeaderToOperations(document);

  // Generate the snake_case version
  const snakeCaseDocument = transformSwaggerDocumentToSnakeCase(document);

  console.log('Original camelCase document generated');
  console.log('Snake_case document generated');

  const swaggerCDN = 'https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.7.2';
  SwaggerModule.setup('doc', app, document, {
    raw: true,
    jsonDocumentUrl: '/api/json',
    yamlDocumentUrl: '/api/yaml',
    customJs: [
      `${swaggerCDN}/swagger-ui.js`,
      `${swaggerCDN}/swagger-ui-bundle.js`,
      `${swaggerCDN}/swagger-ui-standalone-preset.js`,
    ],
    customCssUrl: `${swaggerCDN}/swagger-ui.css`,
  });

  SwaggerModule.setup('doc/camel_case', app, camelCaseDocument, {
    raw: true,
    jsonDocumentUrl: '/api/json/camel_case',
    yamlDocumentUrl: '/api/yaml/camel_case',
    customJs: [
      `${swaggerCDN}/swagger-ui.js`,
      `${swaggerCDN}/swagger-ui-bundle.js`,
      `${swaggerCDN}/swagger-ui-standalone-preset.js`,
    ],
    customCssUrl: `${swaggerCDN}/swagger-ui.css`,
  });

  SwaggerModule.setup('doc/snake_case', app, snakeCaseDocument, {
    raw: true,
    jsonDocumentUrl: '/api/json/snake_case',
    yamlDocumentUrl: '/api/yaml/snake_case',
    customJs: [
      `${swaggerCDN}/swagger-ui.js`,
      `${swaggerCDN}/swagger-ui-bundle.js`,
      `${swaggerCDN}/swagger-ui-standalone-preset.js`,
    ],
    customCssUrl: `${swaggerCDN}/swagger-ui.css`,
  });

  // Enable graceful shutdown
  app.enableShutdownHooks();
  await app.listen(port);

  console.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap();
