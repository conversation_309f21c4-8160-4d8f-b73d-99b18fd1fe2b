'use client';

import { BoardStatusEnum, type BoardWithCountVO } from '@repo/common/types/board/types';
import { MessageAtReferenceTypeEnum } from '@repo/common/types/chat/enum';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { Popover, PopoverContent, PopoverTrigger } from '@repo/ui/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import * as boardIcons from '@youmindinc/youicon';
import { useAtomValue, useSetAtom } from 'jotai';
import { ChevronLeft, Paperclip, Plus, Undo2 } from 'lucide-react';
import { useState } from 'react';
import {
  addFilesToMentionListAtom,
  type MentionFileItem,
  transferBoardItemToMention,
  transferChatAtiToMention,
} from '@/hooks/ask-ai/useChatMention';
import { type BoardWithTreeItemsVO, boardDetailAtom } from '@/hooks/useBoards';
import { callHTTP } from '@/utils/callHTTP';
import { cn } from '@/utils/utils';
import { MentionFilePanel } from '../ask-ai-editor/mention-file-panel';
import { Boards } from '../icon/sidebar';
import { Separator } from '../ui/separator';
import FileUploaderWrapper from './chat-file-uploader';

interface ChatAddMaterialsPanelProps {
  mentionList: MentionFileItem[];
  recommendMentionOptions: MentionFileItem[];
  onAddMention: (mention: MentionFileItem) => void;
}

export function ChatAddMaterialsPanel({
  mentionList,
  recommendMentionOptions,
  onAddMention,
}: ChatAddMaterialsPanelProps) {
  const [open, setOpen] = useState(false);

  const addFilesToMentionList = useSetAtom(addFilesToMentionListAtom);
  const { trackButtonClick } = useTrackActions();

  const handleSelectMention = (item: MentionFileItem) => {
    onAddMention(item);
    setOpen(false);
  };

  const handleAddFiles = (files: File[]) => {
    setOpen(false);
    addFilesToMentionList(files);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          className="flex h-6 w-6 cursor-pointer items-center justify-center rounded-[6px] hover:bg-card-snips"
          onClick={() => {
            trackButtonClick('ask_ai_at_panel_trigger');
          }}
        >
          <Plus size={16} />
        </div>
      </PopoverTrigger>
      <PopoverContent
        className="w-[316px] rounded-[16px] border-none bg-card p-2 text-sm text-foreground shadow-lg"
        align="end"
      >
        <AddMaterialsPanel
          mentionList={mentionList}
          recommendMentionOptions={recommendMentionOptions}
          onAddMention={handleSelectMention}
          handleAddFiles={handleAddFiles}
        />
      </PopoverContent>
    </Popover>
  );
}

function AddMaterialsPanel({
  mentionList,
  recommendMentionOptions,
  onAddMention,
  handleAddFiles,
}: ChatAddMaterialsPanelProps & { handleAddFiles: (files: File[]) => void }) {
  const [search, _setSearch] = useState('');
  const boardDetail = useAtomValue(boardDetailAtom);
  const [boardListFetched, setBoardListFetched] = useState(false);
  const [showBoardList, setShowBoardList] = useState(false);
  const [boards, setBoards] = useState<BoardWithCountVO[]>([]);
  const [selectedBoardDetail, setSelectedBoardDetail] = useState<BoardWithTreeItemsVO | null>(null);
  const [loadingBoards, setLoadingBoards] = useState(false);
  const [loadingBoardDetail, setLoadingBoardDetail] = useState(false);

  const fetchBoards = async () => {
    if (boardListFetched) {
      return;
    }
    setLoadingBoards(true);
    try {
      const res = await callHTTP<BoardWithCountVO[]>('/api/v1/board/listBoards', {
        method: 'POST',
        body: {},
      });
      setBoards((res.data || []).filter((b) => b.status === BoardStatusEnum.IN_PROGRESS));
      setBoardListFetched(true);
    } catch (_e) {
      setBoards([]);
    } finally {
      setLoadingBoards(false);
    }
  };

  const fetchBoardDetail = async (boardId: string) => {
    setLoadingBoardDetail(true);
    try {
      const res = await callHTTP<BoardWithTreeItemsVO>('/api/v1/board/getBoardDetail', {
        method: 'POST',
        body: { id: boardId },
      });
      if (res.data) {
        res.data.board_items.forEach((item) => {
          // @ts-expect-error board_id is not in SnipVO
          item.entity.board_id = boardId;
        });
      }
      setSelectedBoardDetail(res?.data || null);
    } catch (_e) {
      setSelectedBoardDetail(null);
    } finally {
      setLoadingBoardDetail(false);
    }
  };

  const handleSelectBoard = async (board: BoardWithCountVO) => {
    if (board.id === boardDetail?.id) {
      setSelectedBoardDetail(null);
      setShowBoardList(false);
      return;
    }
    setShowBoardList(false);
    await fetchBoardDetail(board.id);
  };

  const renderBoardItem = (board: BoardWithCountVO) => {
    const Icon = boardIcons[board.icon?.name as keyof typeof boardIcons] || boardIcons.Planet;
    return (
      <>
        <Icon size={16} style={{ color: `hsl(var(${board?.icon?.color}))` }} className="mr-1" />
        <span className="max-w-[200px] truncate">{board?.name}</span>
      </>
    );
  };

  const renderBackButton = () => {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            <div
              className="flex items-center text-xs cursor-pointer text-foreground"
              onClick={() => {
                setShowBoardList(false);
                setSelectedBoardDetail(null);
              }}
            >
              <Undo2 size={14} className="mr-[2px]" />
              {'Current'}
            </div>
          </TooltipTrigger>
          <TooltipContent>{'Back to current board'}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const renderBoardSwitcher = () => {
    const board = selectedBoardDetail || boardDetail!;
    if (loadingBoardDetail) {
      return null;
    }
    return (
      <div className="flex items-center justify-between gap-2 p-2 rounded-lg cursor-pointer">
        <div className="flex items-center font-medium">
          <ChevronLeft
            size={16}
            className="mr-2 cursor-pointer"
            onClick={async () => {
              setShowBoardList(true);
              await fetchBoards();
            }}
          />
          {renderBoardItem(board)}
        </div>
        {board !== boardDetail && renderBackButton()}
      </div>
    );
  };

  const renderBoardList = () => (
    <>
      <div className="flex items-center justify-between gap-2 p-2">
        <span className="flex items-center gap-1 text-sm text-foreground">
          <Boards size={16} />
          <span className="text-sm font-medium">{'Boards'}</span>
        </span>
        {renderBackButton()}
      </div>
      <div className="flex max-h-[300px] flex-col overflow-y-auto">
        {loadingBoards ? (
          <div className="relative h-[200px]">
            <SimpleLoading absoluteCenter />
          </div>
        ) : (
          boards.map((b) => (
            <div
              key={b.id}
              className={cn(
                'flex h-9 shrink-0 cursor-pointer items-center justify-between gap-2 rounded-sm px-2 hover:bg-card-snips',
              )}
              onClick={() => handleSelectBoard(b)}
            >
              <div className="flex items-center">{renderBoardItem(b)}</div>
              <span className="text-xs text-caption">
                {b.snips_count + b.thoughts_count || 0 > 1
                  ? `${b.snips_count + b.thoughts_count} items`
                  : `${b.snips_count + b.thoughts_count} item`}
              </span>
            </div>
          ))
        )}
      </div>
    </>
  );

  const handleSelectMention = (item: MentionFileItem) => {
    onAddMention(item);
  };

  const renderBoardItemList = () => {
    const mentionOptions = selectedBoardDetail
      ? selectedBoardDetail.board_items.map((item) => ({
          ...transferBoardItemToMention(item),
          otherBoardEntity: item.entity,
        }))
      : mentionList;
    const recommendOptions: MentionFileItem[] = selectedBoardDetail
      ? [
          ...mentionOptions.slice(0, 5),
          {
            ...transferChatAtiToMention({
              name: selectedBoardDetail.name,
              entity_type: MessageAtReferenceTypeEnum.BOARD,
              entity: selectedBoardDetail,
            }),
            otherBoardEntity: selectedBoardDetail,
          },
        ]
      : recommendMentionOptions;
    return (
      <>
        {renderBoardSwitcher()}
        {loadingBoardDetail ? (
          <div className="relative h-[200px]">
            <SimpleLoading absoluteCenter />
          </div>
        ) : (
          <>
            <div className="rounded-0">
              <MentionFilePanel
                query={search}
                mentionOptions={mentionOptions}
                recommendMentionOptions={recommendOptions}
                showSearch={true}
                onSelect={handleSelectMention}
              />
            </div>
            <div className="p-2">
              <Separator className="bg-card-snips" />
            </div>
            <FileUploaderWrapper onFilesChange={handleAddFiles}>
              <div
                className={cn(
                  'flex cursor-pointer flex-row items-center rounded-lg p-2 hover:bg-card-snips',
                )}
              >
                <div className={cn('flex flex-row items-center gap-2')}>
                  <Paperclip size={16} />
                  <span className="text-sm text-foreground">{'Add files'}</span>
                </div>
              </div>
            </FileUploaderWrapper>
          </>
        )}
      </>
    );
  };

  return <>{showBoardList ? renderBoardList() : renderBoardItemList()}</>;
}
