import { TOOL_TYPES } from '@repo/common/consts/tool/const';
import type { BoardItemVO } from '@repo/common/types/board/types';
import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import type { MessageDiagramGenerateResult } from '@repo/common/types/chat/enum';
import type { CompletionToolBlock } from '@repo/common/types/chat/types';
import { CompletionBlockStatusEnum } from '@repo/common/types/completion';
import type { SnipImageVO } from '@repo/common/types/snip/app-types';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import SVGEditor from '@repo/ui/components/custom/svg-editor';
import { toast } from '@repo/ui/components/ui/sonner';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { omit } from 'lodash';
import { PencilRuler } from 'lucide-react';
import { useState } from 'react';
import { NewSnip } from '@/components/icon/new-snip';
import { updateChatMessageCompletionBlockAtom } from '@/hooks/ask-ai/useSendChatMessage';
import { focusMaterialByEntityIdAtom, panelStateAtom } from '@/hooks/useBoardState';
import { boardDetailAtom, unshiftBoardItemsAtom } from '@/hooks/useBoards';
import { thoughtEditorAtom } from '@/hooks/useThought';
import { callHTTP } from '@/utils/callHTTP';
import { formatSVG } from '@/utils/formatSVG';
import { cn } from '@/utils/utils';
import { ToolStatus, ToolTitle } from './card';
import { useAddBoardItemsWhenBlockRunning } from './hooks/useAddSnipResultToBoard';
import { type TOOL_RENDERER, TOOL_SCENE_TYPE } from './type';

const titles = {
  [CompletionBlockStatusEnum.ING]: 'Creating diagram',
  [CompletionBlockStatusEnum.EXECUTING]: 'Creating diagram',
  [CompletionBlockStatusEnum.DONE]: 'Created diagram',
  [CompletionBlockStatusEnum.ERROR]: 'Failed to create diagram',
};

export const GenerateDiagramCard = ({
  block,
  variant,
  scene,
}: {
  block: CompletionToolBlock;
  variant: 'small' | 'middle';
  scene: TOOL_SCENE_TYPE;
}) => {
  const thoughtEditor = useAtomValue(thoughtEditorAtom);
  const panelState = useAtomValue(panelStateAtom);
  const thoughtOpened = panelState.panelData?.entity_type === 'thought';
  const focusMaterialByEntityId = useSetAtom(focusMaterialByEntityIdAtom);

  const updateChatMessageCompletionBlock = useSetAtom(updateChatMessageCompletionBlockAtom);

  const { status, tool_arguments } = block;
  useAddBoardItemsWhenBlockRunning(block, (block) => {
    const toolResult = block.tool_result as MessageDiagramGenerateResult;
    const { snip } = toolResult;

    if (snip?.status !== 'success') {
      return [];
    }
    return [
      {
        ...snip.vo?.board_item,
        entity_type: BoardItemTypeEnum.SNIP,
        entity: omit(snip.vo, 'board_item'),
      } as BoardItemVO,
    ];
  });

  const isDone = status === CompletionBlockStatusEnum.DONE;
  const isProcessing =
    status === CompletionBlockStatusEnum.ING || status === CompletionBlockStatusEnum.EXECUTING;
  const {
    svg = '',
    image_url = '',
    snip,
  } = (block.tool_result as MessageDiagramGenerateResult) || {};

  const alt = tool_arguments.title;

  const formattedSVG = formatSVG(svg);

  const boardDetail = useAtomValue(boardDetailAtom);
  const [, unshiftBoardItems] = useAtom(unshiftBoardItemsAtom);

  const [isSavingSnip, setIsSavingSnip] = useState(false);
  const handleSaveSnip = async () => {
    setIsSavingSnip(true);
    const boardId = boardDetail?.id;
    try {
      const result = await callHTTP('/api/v1/createImage', {
        method: 'POST',
        body: {
          file: { name: alt, original_url: image_url },
          board_id: boardId,
          title: alt,
        },
      });

      if (result.error) {
        toast('Failed to save image');
      } else if (result.data) {
        const item = result.data as SnipImageVO;
        unshiftBoardItems([
          {
            ...item.board_item!,
            entity: item,
            entity_type: BoardItemTypeEnum.SNIP,
          },
        ]);
        toast('Saved image');
      }
    } catch (_err) {
      toast('Failed to save image');
    } finally {
      setIsSavingSnip(false);
    }
  };

  const handleInsert = () => {
    if (thoughtEditor) {
      try {
        thoughtEditor.commands.insertContentByMarkdown(`![${alt}](${image_url})`);
      } catch (_err) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      }
    }
  };

  const handleSave = async (svg: string) => {
    const { data, error } = await callHTTP('/api/v1/uploadSVG', {
      method: 'POST',
      body: { svg },
    });
    if (error) {
      throw new Error(error.message || '获取上传链接失败');
    }
    if (data) {
      // Update tool_result(image_url & svg code) in completion block
      await callHTTP('/api/v1/chat/updateCompletionBlock', {
        method: 'POST',
        body: {
          block_id: block.id,
          tool_result: { image_url: data.image_url, svg },
        },
      });
      updateChatMessageCompletionBlock({
        message_id: block.message_id,
        block: {
          ...block,
          tool_result: { image_url: data.image_url, svg },
        },
      });
    }
  };

  return (
    <>
      <ToolTitle
        text={renderTitle(block)}
        icon={snip ? <NewSnip size={14} /> : <PencilRuler size={14} />}
        variant={variant}
        blockStatus={block.status}
        className={cn(
          scene === TOOL_SCENE_TYPE.CHAT &&
            block.tool_result.snip?.status === 'success' &&
            'cursor-pointer',
        )}
        onClick={() => {
          if (scene === TOOL_SCENE_TYPE.CHAT && block.tool_result.snip?.status === 'success') {
            focusMaterialByEntityId(block.tool_result.snip.vo.id);
          }
        }}
      />
      {svg && (
        <div className="group relative overflow-hidden rounded-[10px] border bg-card">
          {isDone ? (
            <SVGEditor
              svg={svg}
              src={image_url}
              alt={alt}
              isSavingSnip={isSavingSnip}
              insertToThoughtEnabled={thoughtOpened}
              clickToViewImageEnabled={true}
              editEnabled={true}
              saveSnipEnabled={
                scene !== TOOL_SCENE_TYPE.CUSTOM_ASSISTANT && scene !== TOOL_SCENE_TYPE.NEW_BOARD
              }
              onSaveSnip={handleSaveSnip}
              onInsertToThought={handleInsert}
              onSave={handleSave}
            />
          ) : (
            <>
              {isProcessing && (
                <div className="absolute flex items-center justify-center w-full h-full">
                  <SimpleLoading className="h-[100px]" />
                </div>
              )}
              <div className="svg-container" dangerouslySetInnerHTML={{ __html: formattedSVG }} />
            </>
          )}
        </div>
      )}
    </>
  );
};

const renderTitle = (block: CompletionToolBlock) => {
  const { snip } = (block.tool_result as MessageDiagramGenerateResult) || {};
  let toolTitle = titles[block.status] || titles[CompletionBlockStatusEnum.ING];
  if (snip) {
    const { status } = snip;
    if (status === 'processing') {
      toolTitle = 'Creating snip';
    } else if (status === 'success') {
      toolTitle = 'Created snip';
    } else if (status === 'failed') {
      toolTitle = 'Failed to create snip';
    }
  }
  return (
    <>
      <span>{toolTitle}</span>
      {snip?.vo?.title && (
        <span className="ml-2 text-xs text-caption-foreground">{snip?.vo?.title}</span>
      )}
    </>
  );
};

export const GenerateDiagramTool: TOOL_RENDERER = {
  type: TOOL_TYPES.DIAGRAM_GENERATE,
  renderer: GenerateDiagramCard,
  transformToMarkdown: (block) => {
    const alt = block.tool_arguments.title;
    const { image_url = '' } = (block.tool_result as MessageDiagramGenerateResult) || {};
    return `![${alt}](${image_url})`;
  },
  getToolTitle: (block) => {
    const { snip } = (block.tool_result as MessageDiagramGenerateResult) || {};
    return (
      <ToolStatus
        logo={<PencilRuler size={16} />}
        commandName={titles[block.status]}
        commandDescription={snip?.vo?.title}
      />
    );
  },
  needRefreshBoard: true,
};
