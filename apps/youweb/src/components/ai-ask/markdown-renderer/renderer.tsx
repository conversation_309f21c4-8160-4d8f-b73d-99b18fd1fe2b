import ReactMarkdown from 'react-markdown';
import rehypeKatex from 'rehype-katex';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import 'katex/dist/katex.min.css';
import React, { memo, useEffect, useRef, useState } from 'react';
import { visit } from 'unist-util-visit';
import { ImagePreviewWithBusinessLogic } from '@/components/image-preview/with-business-logic';
import { TOOL_SCENE_TYPE } from '../tool/type';
import { CodeRenderer } from './code';

export const MarkdownRenderer = memo(
  ({
    content,
    className,
    showEndTagWhenBlocked = false,
    citationRender,
    scene,
  }: {
    content: string;
    className: string;
    showEndTagWhenBlocked?: boolean;
    citationRender?: (url: string) => React.ReactNode;
    scene?: TOOL_SCENE_TYPE;
  }) => {
    const contentTimestampRef = useRef<number>(Date.now());
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const prevContentRef = useRef<string>(content);

    const [_showEndTag, setShowEndTag] = useState(false);

    useEffect(() => {
      if (content !== prevContentRef.current) {
        contentTimestampRef.current = Date.now();
        prevContentRef.current = content;
        setShowEndTag(false);
      }

      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }

      if (showEndTagWhenBlocked) {
        timerRef.current = setTimeout(() => {
          const elapsedTime = Date.now() - contentTimestampRef.current;
          if (elapsedTime >= 2000) {
            setShowEndTag(true);
          }
        }, 2000);
      } else {
        setShowEndTag(false);
      }

      return () => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
        }
      };
    }, [content, showEndTagWhenBlocked]);

    return (
      <div className={className}>
        <ReactMarkdown
          remarkPlugins={[
            remarkGfm,
            remarkMath,
            () => (tree) => {
              visit(tree, 'code', (node) => {
                node.lang = node.lang ?? 'plaintext';
              });
            },
          ]}
          rehypePlugins={[rehypeKatex, [rehypeRaw, { passThrough: ['element'] }]]}
          components={{
            code({ className, children, ...props }) {
              return (
                <CodeRenderer code={(children as string) || ''} className={className} {...props} />
              );
            },
            table({ node, ...props }) {
              return (
                <div className="mx-2 my-4 overflow-x-auto rounded-lg bg-card-snips px-6 pb-1">
                  <table {...props} />
                </div>
              );
            },
            th({ node, children, ...props }) {
              return (
                <th
                  {...props}
                  style={{
                    minWidth: `100px`,
                    ...props.style,
                  }}
                >
                  {children}
                </th>
              );
            },
            img({ node, src, alt, ...props }) {
              const inNewBoard = scene === TOOL_SCENE_TYPE.NEW_BOARD;
              const inCustomAssistant = scene === TOOL_SCENE_TYPE.CUSTOM_ASSISTANT;
              return (
                <ImagePreviewWithBusinessLogic
                  src={src || ''}
                  alt={alt || ''}
                  className="rounded-md"
                  saveEnabled={!inCustomAssistant && !inNewBoard}
                  isNewBoard={inNewBoard}
                  {...props}
                />
              );
            },
            a({ node, href, children, ...props }) {
              const textContent = React.isValidElement(children)
                ? (children.props.value as string)
                : Array.isArray(children)
                  ? children.join('')
                  : String(children);

              if (
                href &&
                citationRender &&
                (textContent === 'citation' || textContent === 'source' || textContent === '来源')
              ) {
                return citationRender(href);
              }
              return (
                <a
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="ym-markdown-link"
                  {...props}
                >
                  {children}
                </a>
              );
            },
          }}
        >
          {content}
        </ReactMarkdown>
        {/* {showEndTag && <span className={styles["blinking-cursor"]}></span>} */}
      </div>
    );
  },
);

MarkdownRenderer.displayName = 'MarkdownRenderer';
