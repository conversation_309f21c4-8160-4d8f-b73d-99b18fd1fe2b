import { SnipImageVO } from '@repo/common/types/snip/app-types';
import { useAtom } from 'jotai';
import React from 'react';
import { ImagePreviewWithBusinessLogic } from '@/components/image-preview/with-business-logic';
import { snipDetail<PERSON>tom } from '@/hooks/scoped/useSnips';
import { cn } from '@/utils/utils';

export interface ImageProps extends React.HTMLAttributes<HTMLDivElement> {}

export default function Image({ className }: ImageProps) {
  const [snip] = useAtom(snipDetailAtom);
  const { file, title } = snip as SnipImageVO;

  // @ts-expect-error image is dynamic
  const src = file.url;

  return (
    <div
      className={cn(
        'flex h-full w-full flex-1 flex-col items-center justify-center text-lg',
        className,
      )}
    >
      {src && (
        <div className="flex h-full w-full items-center justify-center overflow-hidden px-[1em]">
          <ImagePreviewWithBusinessLogic
            src={src}
            alt={title}
            insertToThoughtEnabled={false}
            saveEnabled={false}
            className="h-full w-full object-contain"
            containerClassName="h-full w-full pb-4"
          />
        </div>
      )}
    </div>
  );
}
