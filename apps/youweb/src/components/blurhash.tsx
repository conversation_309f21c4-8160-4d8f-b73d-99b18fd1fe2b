import { Blurhash as Bh } from 'react-blurhash';
import { cn } from '@/utils/utils';

interface BlurhashProps extends React.HTMLAttributes<HTMLDivElement> {
  metadata?: ImageMetadata;
}

export interface ImageMetadata {
  width: number;
  height: number;
  blurhash: string;
}

export const Blurhash: React.FC<BlurhashProps> = ({ metadata, className }) => {
  return metadata?.blurhash ? (
    <div
      className={cn('center size-full max-w-full', className)}
      style={{
        aspectRatio:
          metadata?.height && metadata?.width ? metadata.width / metadata.height : undefined,
      }}
    >
      <Bh hash={metadata?.blurhash} resolutionX={32} resolutionY={32} className="!size-full" />
    </div>
  ) : (
    <></>
  );
};
