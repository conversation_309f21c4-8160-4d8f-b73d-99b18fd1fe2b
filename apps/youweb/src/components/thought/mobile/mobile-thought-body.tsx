import { GetThoughtDto } from '@repo/api/generated-client/snake-case/models/GetThoughtDto';
import { ThoughtDto } from '@repo/api/generated-client/snake-case/models/ThoughtDto';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import type { Editor } from '@tiptap/core';
import { methodRegistry } from '@youmindinc/jsbridge';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import type { Doc } from 'yjs';
import { apiClient, callAPI } from '@/utils/callHTTP';
import type { EditorOnReadyParams } from '../../editor-kit/thought-mobile-editor/mobile-editor';
import type { ThoughtBodyComponentRef } from '../type';
import { MobileThoughtWorkflow } from '../workflow';
import { ThoughtRegisterToNativeMethodName } from './const';
import { getMobileThoughtBodyExtensionOptions } from './mobile-extension-options';
import { MobileThoughtBodyComponent } from './mobile-thought-body-component';

const URL_THOUGHT_ID_PARAM = 'thoughtId';

export interface MobileThoughtBodyProps {
  onCreate?: (params: EditorOnReadyParams) => void;
}

export const MobileThoughtBody = (props: MobileThoughtBodyProps) => {
  const componentRef = useRef<ThoughtBodyComponentRef>(null);
  const [editor, setEditor] = useState<Editor | null>(null);
  const [ydoc, setYdoc] = useState<Doc | null>(null);
  const [thought, setThought] = useState<ThoughtDto | null>(null);
  const [loadingThought, setLoadingThought] = useState(false);
  const [searchParams] = useSearchParams();

  // 从 URL 参数获取 thoughtId，如果 props 中没有传入 id 的话
  const idInUrl = searchParams.get(URL_THOUGHT_ID_PARAM) || undefined;

  const workflow = useMemo(() => {
    if (!editor || !ydoc || !componentRef.current) {
      return null;
    }
    const workflow = new MobileThoughtWorkflow({
      id: idInUrl ?? thought?.id,
      editor,
      ydoc,
      componentRef,
    });
    editor.commands.setWorkflow(workflow);
    return workflow;
  }, [editor, idInUrl, ydoc, componentRef, thought]);

  useEffect(() => {
    return () => {
      workflow?.destroy();
    };
  }, [workflow]);

  useEffect(() => {
    methodRegistry.exportMethod(ThoughtRegisterToNativeMethodName.GET_THOUGHT_DATA, async () => {
      return workflow?.getThoughtData();
    });

    methodRegistry.exportMethod(
      ThoughtRegisterToNativeMethodName.SET_THOUGHT_DATA,
      async (data: ThoughtDto) => {
        setThought(data);
        return { success: true };
      },
    );
  }, [thought, workflow]);

  useEffect(() => {
    if (!idInUrl) {
      return;
    }
    setLoadingThought(true);

    const params: GetThoughtDto = {
      id: idInUrl,
    };

    const fetchThought = async () => {
      try {
        const { data } = await callAPI(apiClient.thoughtApi.getThought(params), {
          silent: true,
        });
        setLoadingThought(false);
        setThought(data ?? null);
      } catch (error) {
        console.error('Failed to fetch thought:', error);
        setLoadingThought(false);
        return;
      }
    };

    fetchThought();
  }, [idInUrl]);

  if (loadingThought) {
    return (
      <div className="flex h-[100vh] w-full items-center justify-center pb-20">
        <SimpleLoading />
      </div>
    );
  }

  return (
    <MobileThoughtBodyComponent
      key={thought?.id}
      ref={componentRef}
      workflow={workflow}
      id={thought?.id ?? ''}
      thought={thought}
      mobileEditorProps={{
        id: idInUrl ?? thought?.id,
        content: thought?.content.raw,
        storeOptions: {
          indexeddbStoreEnable: false,
        },
        onCreate: (params) => {
          setEditor(params.editor);
          setYdoc(params.ydoc);
          props.onCreate?.(params);
        },
        extensionsOptions: getMobileThoughtBodyExtensionOptions(),
      }}
    />
  );
};
