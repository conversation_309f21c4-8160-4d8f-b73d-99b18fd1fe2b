import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { Dialog, DialogContent } from '@repo/ui/components/ui/dialog';
import { sendTrackEvent } from '@repo/ui/lib/posthog/utils';
import type { Editor } from '@tiptap/react';
import { X } from 'lucide-react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { getWorkflow } from '../../editor-kit/extensions/thought-adpater-extension';
import { THOUGHT_EVENT_REPORT_KEY } from '../const';
import { HistoryInfo } from './history-info';
import { HistoryPreviewContent } from './history-preview-content';
import type { ThoughtVersion } from './tem-type';

interface ThoughtHistoryModalProps {
  editor?: Editor;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  versionList?: ThoughtVersion[];
}

export const ThoughtHistoryModal = ({
  editor,
  open,
  onOpenChange,
  versionList: propsVersionList,
}: ThoughtHistoryModalProps) => {
  const [loadingHistory, setLoadingHistory] = useState(true);
  const [versionList, setVersionList] = useState<ThoughtVersion[]>(propsVersionList || []);
  const [curThought, setCurThought] = useState<ThoughtVersion | undefined>(propsVersionList?.[0]);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    sendTrackEvent(THOUGHT_EVENT_REPORT_KEY.THOUGHT_HISTORY_MODAL_OPEN);
  }, []);

  const getVersionList = useCallback(async () => {
    setLoadingHistory(true);
    if (!editor) {
      setLoadingHistory(false);
      return;
    }
    const workflow = getWorkflow(editor);
    if (!workflow) {
      return;
    }
    setLoadingHistory(true);
    const versionList = await workflow.getVersionList();
    setLoadingHistory(false);
    setVersionList(versionList);
    setCurThought(versionList[0]);
  }, [open]);

  // 删除版本的处理函数
  const handleDeleteVersion = useCallback(
    (versionToDelete: ThoughtVersion) => {
      if (!editor) {
        return;
      }
      const workflow = getWorkflow(editor);
      if (workflow) {
        workflow.deleteVersion(versionToDelete.id).then((res) => {
          if (!res) {
            // editor.commands.showFailToast("Failed to delete version");
          }
        });
      }
      const newVersionList = versionList.filter((version) => version.id !== versionToDelete.id);
      setVersionList(newVersionList);
      // 如果删除的是当前选中的版本，需要选择新的当前版本
      if (curThought?.id === versionToDelete.id) {
        if (newVersionList.length > 0) {
          // 找到删除版本在原列表中的索引位置
          const deletedIndex = versionList.findIndex(
            (version) => version.id === versionToDelete.id,
          );
          // 优先选择后面的版本
          if (deletedIndex < newVersionList.length) {
            // 后面有版本，选择删除位置的版本（因为删除后，后面的版本会前移到当前位置）
            setCurThought(newVersionList[deletedIndex]);
          } else {
            // 后面没有版本，选择前面的版本（最后一个版本）
            setCurThought(newVersionList[newVersionList.length - 1]);
          }
        } else {
          // 如果列表为空，设置为 undefined
          setCurThought(undefined);
        }
      }
    },
    [versionList, curThought],
  );

  // 新增版本的处理函数
  const handleAddVersion = useCallback(
    (newVersion: ThoughtVersion) => {
      // 将新版本添加到列表的开头（最新的版本在前面）
      const newVersionList = [newVersion, ...versionList];
      setVersionList(newVersionList);
      // 自动选中新创建的版本
      setCurThought(newVersion);
    },
    [versionList],
  );

  useEffect(() => {
    if (!open) {
      return;
    }
    getVersionList();
  }, [open, getVersionList]);

  useEffect(() => {
    if (scrollContainerRef.current) {
      setTimeout(() => {
        scrollContainerRef.current?.scrollTo({
          top: 0,
        });
      }, 200);
    }
  }, [curThought?.id]);

  const renderContent = () => {
    return (
      <>
        <div className="py-4 pl-5">
          <div
            ref={scrollContainerRef}
            className="h-full shrink-[100] grow overflow-y-auto rounded-lg border border-muted px-8 py-5 shadow-md"
          >
            {versionList.length === 0 || !curThought ? (
              <div className="flex h-full w-[41.5rem] items-center justify-center">
                No history available
              </div>
            ) : (
              <HistoryPreviewContent thought={curThought} />
            )}
          </div>
        </div>
        <div className="w-[20.625rem] flex-1 px-2 py-5">
          <HistoryInfo
            onOpenChange={onOpenChange}
            curThought={curThought}
            editor={editor}
            thoughtVersionList={versionList}
            onSetCurThought={setCurThought}
            onDeleteVersion={handleDeleteVersion}
            onAddVersion={handleAddVersion}
            onRestoreSuccess={() => {
              onOpenChange(false);
            }}
          />
        </div>
      </>
    );
  };

  const renderContentWidthCloseIcon = (component: React.ReactNode) => {
    return (
      <div className="flex flex-col items-center justify-center w-full h-full">
        <div className="flex justify-end w-full p-4 h-14">
          <div
            className="flex items-center justify-center w-6 h-6 rounded-md cursor-pointer hover:bg-card-snips"
            onClick={() => {
              onOpenChange(false);
            }}
          >
            <X size={18} />
          </div>
        </div>
        <div className="flex-1">{component}</div>
      </div>
    );
  };

  const renderLoading = () => {
    return renderContentWidthCloseIcon(<SimpleLoading />);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="h-[42.5rem] w-[67.5rem] min-w-[67.5rem] bg-white p-0"
        showCloseButton={false}
        overlayClassName="bg-black/45"
      >
        <div className="flex h-[42.5rem] w-full items-stretch">
          {loadingHistory ? renderLoading() : renderContent()}
        </div>
      </DialogContent>
    </Dialog>
  );
};
