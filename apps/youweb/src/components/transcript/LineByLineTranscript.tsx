'use client';

import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { AnimatePresence, motion } from 'framer-motion';
import { useAtom } from 'jotai';
import { MouseEvent, memo, useEffect, useRef, useState } from 'react';
import { PreviewIcon } from '@/components/icon/preview';
import { useTranslation } from '@/hooks/useTranslation';
import { cn } from '@/utils/utils';

import { isGeneratingTranscriptAtom, lineByLineRefAtom, unformattedCuesAtom } from './atoms';

interface LineByLineTranscriptProps {
  handleTogglePlay?: () => void;
  seek: (timestamp: number | string) => void;
}

export const LineByLineTranscript = memo(function LineByLineTranscript({
  handleTogglePlay,
  seek,
}: LineByLineTranscriptProps) {
  const { t } = useTranslation('Action.Transcript');

  const [unformattedCues] = useAtom(unformattedCuesAtom);
  const [isGeneratingTranscript] = useAtom(isGeneratingTranscriptAtom);
  const [, setLineByLineRef] = useAtom(lineByLineRefAtom);

  const lineByLineRef = useRef<HTMLOListElement>(null);

  const [isDragging, setIsDragging] = useState(false);
  const [initialPosition, setInitialPosition] = useState({ x: 0, y: 0 });

  const handleMouseDown = (e: MouseEvent) => {
    setInitialPosition({ x: e.clientX, y: e.clientY });
    setIsDragging(false); // 初始化为非拖拽状态
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) {
      const distanceX = Math.abs(e.clientX - initialPosition.x);
      const distanceY = Math.abs(e.clientY - initialPosition.y);
      const threshold = 5; // 设置阈值距离
      if (distanceX > threshold || distanceY > threshold) {
        setIsDragging(true);
      }
    }
  };

  const handleMouseUp = (timestamp: string) => {
    if (!isDragging) {
      handleTogglePlay?.();
      seek(timestamp!);
    }
  };

  useEffect(() => {
    setLineByLineRef(lineByLineRef);
  }, [setLineByLineRef]);

  const renderGeneratingLoading = (
    <>
      {!!unformattedCues?.length && (
        <div
          className="p-4 mb-4 body-strong rounded-xl"
          style={{
            background:
              'linear-gradient(94.53deg, rgba(250, 240, 247, 0.68) -0.77%, rgba(241, 238, 251, 0.68) 98.61%)',
          }}
        >
          <div className="flex items-center mb-4 gap-x-2">
            <PreviewIcon size={16} />
            {t('preview')}
          </div>
          <ol className="flex flex-col text-base body gap-y-3" ref={lineByLineRef}>
            {unformattedCues?.map(({ timestamp, content }, i) => {
              return (
                <li key={i} className="flex flex-row rounded-lg">
                  <div className="w-[64px]">
                    <div
                      className={cn(
                        'mr-4 h-[24px] rounded-sm bg-card-snips px-[4px] py-[3px] text-center text-sm !font-normal leading-[18px]',
                      )}
                    >
                      {timestamp}
                    </div>
                  </div>
                  <p className="flex-1 whitespace-pre-wrap">{content}</p>
                </li>
              );
            })}
          </ol>
        </div>
      )}
      <p className="mb-4 body text-muted-foreground">
        {t('generating')}
        <div className="inline-block ml-4 scale-50">
          <SimpleLoading
            variant="dot-stretching"
            className="inline-block h-auto min-h-[auto] w-auto"
          />
        </div>
      </p>
    </>
  );

  return (
    <>
      {!isGeneratingTranscript && (
        <ol className="flex flex-col" ref={lineByLineRef}>
          {unformattedCues?.map(({ timestamp, content }, i) => {
            return (
              <li
                data-timestamp={timestamp}
                key={i}
                className="flex flex-row px-2 py-2 -mx-2 transition-colors rounded-md cursor-pointer hover:bg-muted hover:text-foreground"
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={() => handleMouseUp(timestamp)}
              >
                <div className="w-[64px]">
                  <div
                    className={cn(
                      'mr-4 h-[24px] rounded-sm bg-card-snips px-[4px] py-[3px] text-center text-sm !font-normal leading-[18px]',
                    )}
                  >
                    {timestamp}
                  </div>
                </div>
                <p className="flex-1 whitespace-pre-wrap focus:underline focus:decoration-link focus:underline-offset-4">
                  {content}
                </p>
              </li>
            );
          })}
        </ol>
      )}
      <AnimatePresence>
        {isGeneratingTranscript && (
          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            {renderGeneratingLoading}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
});
