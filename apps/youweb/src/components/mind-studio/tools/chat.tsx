import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { ButtonWithTooltip } from '@repo/ui/components/ui/button';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { History, Loader2, Plus } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { AiAskChatBox } from '@/components/ai-ask';
import { ChatHistory } from '@/components/ai-ask/chat-history';
import { ChatStatusEnum } from '@/hooks/ask-ai/types';
import { chatHistoryDataAtom, chatHistoryOpenAtom } from '@/hooks/ask-ai/useChatHistory';
import {
  clearChatMessageStatusAtom,
  createAssistantChatInHistoryAtom,
  createOrGetAssistantChatAtom,
  switchChatAtom,
} from '@/hooks/ask-ai/useChatInitialization';
import { getBoardChatAtAtom, transferBoardItemToChatAt } from '@/hooks/ask-ai/useChatMention';
import {
  chatAtom,
  chatSwitching<PERSON>tom,
  setChatModelWithLastMessageAtom,
} from '@/hooks/ask-ai/useChatPanelState';
import { panelStateAtom } from '@/hooks/useBoardState';
import { boardDetailAtom, boardItemsAtom, getActiveBoardItemAtom } from '@/hooks/useBoards';
import useRequestWithAbortController from '@/hooks/useRequestWithAbort';

export default function ChatAssistant() {
  const panelData = useAtomValue(panelStateAtom).panelData;
  const boardId = useAtomValue(boardDetailAtom)!.id;
  const createOrGetAssistantChat = useSetAtom(createOrGetAssistantChatAtom);
  const clearChatMessageStatus = useSetAtom(clearChatMessageStatusAtom);
  const [chatHistoryOpen, setChatHistoryOpen] = useAtom(chatHistoryOpenAtom);
  const setChatModelWithLastMessage = useSetAtom(setChatModelWithLastMessageAtom);
  const setChatHistory = useSetAtom(chatHistoryDataAtom);
  const switchChat = useSetAtom(switchChatAtom);

  const [chat] = useAtom(chatAtom);
  const [chatSwitching] = useAtom(chatSwitchingAtom);

  const boardItemList = useAtomValue(boardItemsAtom);
  const chatAtOptions = useMemo(() => {
    return boardItemList
      .map((item) => transferBoardItemToChatAt(item))
      .map((item) => ({
        ...item,
        entity: {
          ...item.entity,
          board_id: boardId,
        },
      }));
  }, [boardItemList]);
  const currentMaterial = useAtomValue(getActiveBoardItemAtom);
  const boardChatAt = useAtomValue(getBoardChatAtAtom);
  const currentMaterialMemo = useMemo(() => {
    if (!currentMaterial) {
      return boardChatAt;
    }
    return transferBoardItemToChatAt(currentMaterial);
  }, [currentMaterial]);

  useEffect(() => {
    return () => {
      setChatHistoryOpen(false);
      setChatHistory([]);
    };
  }, []);

  const { loading } = useRequestWithAbortController(
    async (abortSignal) => createOrGetAssistantChat(boardId, abortSignal),
    {
      refreshDeps: [],
      stopWhenUnmount: true,
    },
  );

  if (panelData?.entity_type === BoardItemTypeEnum.CHAT) {
    return null;
  }
  if (chatHistoryOpen) {
    return (
      <ChatHistory
        onSelect={async (chatId) => {
          setChatHistoryOpen(false);
          await switchChat(chatId);
          setChatModelWithLastMessage();
          clearChatMessageStatus();
        }}
      />
    );
  }
  if (loading || chatSwitching) {
    return <SimpleLoading />;
  }

  return (
    <div className="flex h-full flex-col" id="mind-studio-chat-panel">
      <ChatHeader />
      <div className="min-h-0 w-full flex-1">
        <AiAskChatBox
          className="px-0"
          chatAtOptions={chatAtOptions}
          currentMaterial={currentMaterialMemo}
          key={chat?.id}
        />
      </div>
    </div>
  );
}

export function ChatHeader() {
  const chat = useAtomValue(chatAtom);
  const setChatHistoryOpen = useSetAtom(chatHistoryOpenAtom);
  const boardId = useAtomValue(boardDetailAtom)!.id;
  const createAssistantChatInHistory = useSetAtom(createAssistantChatInHistoryAtom);
  const { loading, run: createChat } = useRequestWithAbortController(
    async (abortSignal) =>
      createAssistantChatInHistory({
        boardId,
        abortSignal,
      }),
    {
      manual: true,
      refreshDeps: [],
      stopWhenUnmount: true,
    },
  );
  return (
    <div className="mb-2 flex h-6 items-center justify-between">
      <div className="min-w-0 flex-1 truncate pr-2 text-sm text-caption">
        {chat?.title || 'New Chat'}
      </div>
      <div className="flex items-center">
        <ButtonWithTooltip
          onClick={() => {
            setChatHistoryOpen(true);
          }}
          iconOnly
          tooltip="History"
          className="text-muted-foreground"
        >
          <History size={16} />
        </ButtonWithTooltip>

        <ButtonWithTooltip
          onClick={() => {
            setChatHistoryOpen(false);
            createChat();
          }}
          tooltip="New chat"
          disabled={chat?.status === ChatStatusEnum.NOT_STARTED || !chat || loading}
          className="text-muted-foreground"
          iconOnly
        >
          {loading ? <Loader2 size={16} className="animate-spin" /> : <Plus size={16} />}
        </ButtonWithTooltip>
      </div>
    </div>
  );
}
