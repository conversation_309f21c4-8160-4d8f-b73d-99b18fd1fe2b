import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { Button } from '@repo/ui/components/ui/button';
import { Input } from '@repo/ui/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@repo/ui/components/ui/popover';
import { toast } from '@repo/ui/components/ui/sonner';
import { Textarea } from '@repo/ui/components/ui/textarea';
import { Loader2, Pen, RefreshCcw } from 'lucide-react';
import { useEffect, useState } from 'react';
import type { ImageSnippetVO } from '@/hooks/scoped/useSnips';
import { useTranslation } from '@/hooks/useTranslation';
import { callHTTP } from '@/utils/callHTTP';
import { cn } from '@/utils/utils';

interface Props {
  snip: ImageSnippetVO;
  setSnipDetail: (snip: ImageSnippetVO) => void;
}

export const ImageTitlePanel: React.FC<Props> = ({ snip, setSnipDetail }) => {
  const [isHover, setIsHover] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [title, setTitle] = useState(snip.title);
  const [description, setDescription] = useState(snip.description);

  const { t } = useTranslation('Library.SnipDetail');

  useEffect(() => {
    if (isEdit) return;
    setTitle(snip.title);
    setDescription(snip.description);
  }, [snip.title, isEdit]);

  const handleCancel = () => {
    setIsEdit(false);
    setIsHover(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title?.trim()) {
      toast(t('titleRequired'));
      return;
    }

    if (title === snip.title && description === snip.description) {
      setIsEdit(false);
      setIsHover(false);
      return;
    }

    setIsLoading(true);
    const { data, error } = await callHTTP('/api/v1/snip/updateImage', {
      method: 'POST',
      body: {
        id: snip.id,
        title: title.trim(),
        description: description?.trim(),
      },
      silent: true,
    });
    setIsEdit(false);
    setIsHover(false);
    setIsLoading(false);

    if (error) {
      toast(t('saveFailed'));
      return;
    }

    setSnipDetail({
      ...snip,
      ...data,
    });
  };

  const handleRefresh = () => {
    setIsGenerating(true);
    callHTTP('/api/v1/snip/generateImageInfo', {
      method: 'POST',
      body: {
        id: snip.id,
      },
    })
      .then(({ data, error }) => {
        if (error) {
          toast(t('generateFailed'));
          return;
        }
        setSnipDetail({
          ...snip,
          ...data,
        });
      })
      .catch((err) => {
        console.error('err', err);
        toast(t('generateFailed'));
      })
      .finally(() => {
        setIsGenerating(false);
      });
  };

  const renderDesc = () => {
    if (!isEdit) {
      if (!snip.description) {
        return null;
      }
      return <div className="mt-3 text-sm break-all">{description}</div>;
    }
    return (
      <Textarea
        className="mt-3 min-h-[160px] w-full rounded-[6px] bg-transparent"
        value={description}
        disabled={isLoading}
        onChange={(e) => setDescription(e.target.value)}
        placeholder={t('imageDescriptionPlaceholder')}
        maxLength={2048}
      />
    );
  };

  const renderContent = () => {
    if (isGenerating) {
      return <SimpleLoading className="min-h-[44px]" />;
    }
    if (isEdit) {
      return (
        <form onSubmit={handleSubmit}>
          <Input
            className="w-full rounded-[6px] bg-transparent text-base font-medium"
            value={title}
            disabled={isLoading}
            onChange={(e) => setTitle(e.target.value)}
            placeholder={t('imageTitlePlaceholder')}
            required
            maxLength={2048}
          />
          {renderDesc()}
          <div className="flex justify-end w-full gap-2 mt-3">
            <Button
              type="button"
              className="h-6 rounded-full bg-transparent px-[10px] text-xs font-medium"
              variant="outline"
              disabled={isLoading}
              onClick={handleCancel}
            >
              {t('cancel')}
            </Button>
            <Button
              type="submit"
              className="h-6 rounded-full px-[10px] text-xs font-medium"
              disabled={isLoading}
            >
              {t('done')}
              {isLoading ? <Loader2 size={14} className="ml-1 animate-spin" /> : null}
            </Button>
          </div>
        </form>
      );
    }
    return (
      <>
        <div className="flex items-center justify-between">
          <div className="flex-1 overflow-hidden text-base font-medium break-all line-clamp-1">
            {title}
          </div>
          {isHover && (
            <>
              <Popover>
                <PopoverTrigger
                  className="flex items-center pl-4 text-muted-foreground hover:text-foreground"
                  // onClick={handleRefresh}
                >
                  <RefreshCcw size={16} />
                </PopoverTrigger>
                <PopoverContent
                  align="end"
                  alignOffset={-36}
                  className="w-[240px] rounded-[10px] border-none"
                >
                  <div className="text-sm">
                    {`Regenerate the title and description. This will replace the current content. `}
                  </div>
                  <div className="flex justify-end mt-2">
                    <Button
                      className="h-6 rounded-full px-[10px] text-xs font-medium"
                      onClick={handleRefresh}
                    >
                      {`Regenerate`}
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
              <button
                className="flex items-center gap-[6px] pl-4 text-muted-foreground hover:text-foreground"
                onClick={() => setIsEdit(true)}
              >
                <Pen size={16} />
              </button>
            </>
          )}
        </div>
        {renderDesc()}
      </>
    );
  };

  return (
    <div
      className={cn(
        'relative left-[-10px] w-[calc(100%+20px)] rounded-[12px] p-[10px]',
        !isEdit && !isGenerating && 'hover:bg-card-snips',
      )}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      {renderContent()}
    </div>
  );
};
