import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { Check, Copy } from 'lucide-react';
import { useState } from 'react';
import { OCRIcon } from '@/components/icon/ocr';
import type { ImageSnippetVO } from '@/hooks/scoped/useSnips';
import { useTranslation } from '@/hooks/useTranslation';
import { callHTTP } from '@/utils/callHTTP';
import { cn } from '@/utils/utils';

interface Props {
  snip: ImageSnippetVO;
  setSnipDetail: (snip: ImageSnippetVO) => void;
}

const NO_TEXT_FOUND = 'No text found';

export const ExtractTextPanel: React.FC<Props> = ({ snip, setSnipDetail }) => {
  const { t } = useTranslation('Library.SnipDetail');
  const [loading, setLoading] = useState(false);

  const [content, setContent] = useState(snip.extracted_text);

  const [copied, setCopied] = useState(false);

  // @ts-expect-error file is optional
  const imageUrl = snip.file?.url;

  const saveText = async (text: string) => {
    const { data, error } = await callHTTP(`/api/v1/snip/updateImage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: snip.id,
        extracted_text: text,
      }),
    });
    if (error) {
      return;
    }
    // 更新 Snip
    setSnipDetail({
      ...snip,
      ...data,
    });
  };

  const handleExtractText = async () => {
    if (loading) return;
    if (!imageUrl) return;

    setLoading(true);
    const { data, error } = await callHTTP('/api/v1/extractText', {
      method: 'POST',
      body: {
        url: imageUrl,
        adaptImage: true,
      },
    });
    setLoading(false);
    if (error) {
      console.error('Extract text failed:', error);
      setContent(t('extractText.failed'));
      return;
    }

    const { text } = data;

    // 处理文本结果，去掉最后一行
    const finalText = text.split('\n').slice(0, -1).join('\n');
    setContent(finalText || NO_TEXT_FOUND);
    if (finalText) {
      saveText(finalText);
    }
  };

  const handleCopy = () => {
    if (!content) return;
    navigator.clipboard.writeText(content);
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  return (
    <>
      <div className="mb-3 flex h-5 w-full items-center justify-between">
        <div className="font-medium text-foreground">{t('extractText.title')}</div>
        {content && (
          <button
            onClick={handleCopy}
            className="text-caption transition-all hover:text-foreground"
          >
            {copied ? <Check size={14} /> : <Copy size={14} />}
          </button>
        )}
      </div>
      <div className="flex flex-col">
        {!content ? (
          <div
            onClick={handleExtractText}
            className={cn(
              'flex min-h-[64px] flex-col items-center justify-center gap-1 rounded-[8px] bg-card-snips px-4 py-3',
              loading ? 'cursor-not-allowed' : 'cursor-pointer',
            )}
          >
            {loading ? (
              <SimpleLoading className="!min-h-[24px]" />
            ) : (
              <>
                <OCRIcon size={18} />
                <div className="text-xs text-caption">{t('extractText.clickToExtract')}</div>
              </>
            )}
          </div>
        ) : (
          <div
            className={cn(
              'whitespace-break-spaces rounded-lg bg-card-snips p-4 text-sm',
              content === NO_TEXT_FOUND ? 'text-disabled-foreground' : 'text-foreground',
            )}
          >
            {content || NO_TEXT_FOUND}
          </div>
        )}
      </div>
    </>
  );
};
