import { BoardGroup } from '@repo/common/types/board-group/types';
import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import { SnipImageVO } from '@repo/common/types/snip/app-types';
import { SnipTypeEnum } from '@repo/common/types/snip/types';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { useAtomValue } from 'jotai';
import { getActiveBoardItemAtom } from '@/hooks/useBoards';
import { mindWorkspaceStateAtom } from '@/hooks/useMindStudio';
import { cn } from '@/utils/utils';
import { GroupMaterial } from '../mind-material/Group';
import SnipMaterial from '../mind-material/Snip';
import ThoughtMaterial from '../mind-material/Thought';

interface MindMaterialViewProps {
  boardId?: string;
  placeholder?: React.ReactNode;
  loading?: boolean;
  showMaterialContainer?: boolean;
  useMaxWidth?: boolean;
  className?: string;
  hideHeader?: boolean;
}

export function MindMaterialView({
  boardId,
  placeholder,
  loading,
  showMaterialContainer = true,
  useMaxWidth = true,
  className,
  hideHeader = false,
}: MindMaterialViewProps) {
  const { currentMaterialType: materialType, currentMaterialId: materialId } =
    useAtomValue(mindWorkspaceStateAtom);
  const activeBoardItem = useAtomValue(getActiveBoardItemAtom);

  const renderMaterial = () => {
    if (loading) {
      return <SimpleLoading />;
    }

    switch (materialType) {
      case BoardItemTypeEnum.SNIP:
        return <SnipMaterial key={materialId} />;
      case BoardItemTypeEnum.THOUGHT:
        return <ThoughtMaterial key={materialId} />;
      case BoardItemTypeEnum.BOARD_GROUP:
        // group 要单独渲染，不带外面的白底外壳
        return null;
      // return <GroupMaterial key={materialId} />;
    }
  };

  if (!materialType || !materialId) {
    return placeholder;
  }
  if (materialType === BoardItemTypeEnum.BOARD_GROUP) {
    if (!activeBoardItem) {
      return null;
    }
    return (
      <GroupMaterial
        key={materialId}
        boardId={boardId!}
        name={(activeBoardItem.entity as BoardGroup).name}
        groupItemId={activeBoardItem!.id}
        hideHeader={hideHeader}
      />
    );
  }
  return (
    <div
      // 加个标识，Note 高亮需要用到
      id="mind-material-view"
      className={cn('relative h-full rounded-[12px]', showMaterialContainer && 'bg-card shadow-md')}
      data-entity-id={materialId}
    >
      <div
        className="group/material-view h-full w-full overflow-y-auto"
        id="mind-material-view-scroll-container"
        key={materialId || 'mind-material'}
      >
        <div
          className={cn(
            'mx-auto h-full pt-5',
            showMaterialContainer && 'px-10',
            useMaxWidth &&
              (activeBoardItem?.entity as SnipImageVO)?.type !== SnipTypeEnum.IMAGE &&
              'max-w-[800px]',
            className,
            // thought 的左右 padding 需要单独处理, 以展示右边 toolbar
            materialType === BoardItemTypeEnum.THOUGHT && 'px-14',
          )}
        >
          {renderMaterial()}
        </div>
      </div>
    </div>
  );
}
