import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { Button } from '@repo/ui/components/ui/button';
import { Input } from '@repo/ui/components/ui/input';
import { Separator } from '@repo/ui/components/ui/separator';
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut } from 'lucide-react';
import type { PageViewport, PDFDocumentProxy } from 'pdfjs-dist';
import { type CSSProperties, useEffect, useMemo, useRef, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { VariableSizeList as List } from 'react-window';
import { useDebounceCallback, useResizeObserver } from 'usehooks-ts';
import { useTranslation } from '@/hooks/useTranslation';
import { cn } from '@/utils/utils';

import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

type Size = {
  width?: number;
  height?: number;
};

// @see https://github.com/wojtekmaj/react-pdf/issues/1811#issuecomment-2157866061
if (typeof Promise.withResolvers === 'undefined') {
  if (window)
    // @ts-expect-error This does not exist outside of polyfill which this is doing
    window.Promise.withResolvers = () => {
      let resolve, reject;
      const promise = new Promise((res, rej) => {
        resolve = res;
        reject = rej;
      });
      return { promise, resolve, reject };
    };
}

// @see https://github.com/wojtekmaj/react-pdf?tab=readme-ov-file#use-external-cdn
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

// @see https://github.com/wojtekmaj/react-pdf?tab=readme-ov-file#support-for-non-latin-characters
const options = {
  cMapUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/cmaps/`,
};

export default function PDFViewer({
  url,
  className,
  toolbarClassName,
}: {
  url: string;
  className?: string;
  toolbarClassName?: string;
}) {
  const { t } = useTranslation('Library.SnipDetail.PDFSource');

  const [toolbarEnabled, setToolbarEnabled] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  const [{ width, height }, setSize] = useState<Size>({
    width: undefined,
    height: undefined,
  });

  const onResize = useDebounceCallback(setSize, 200);
  useResizeObserver({
    ref,
    onResize,
  });
  const [maxWidth, setMaxWidth] = useState(720);
  const [scale, setScale] = useState(1);
  const [zooming, setZooming] = useState(false);

  useEffect(() => {
    // @see https://github.com/bvaughn/react-window/issues/729
    listRef.current?.resetAfterIndex(0); // clear cache
  }, [width]);

  const listRef = useRef<List>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pdf, setPdf] = useState<PDFDocumentProxy | null>(null);
  const [pageViewports, setPageViewports] = useState<PageViewport[] | null>(null);

  const onDocumentLoadSuccess = async (doc: PDFDocumentProxy) => {
    setPdf(doc);
    setToolbarEnabled(true);
  };

  const onItemClick = ({ pageNumber: itemPageNumber }: { pageNumber: number }) => {
    setPageNumber(itemPageNumber);
  };

  const handleZoomIn = () => {
    setZooming(true);
    setMaxWidth(Math.min(maxWidth + 100, 2000));

    setTimeout(() => {
      setZooming(false);
    }, 300);
  };
  const handleZoomOut = () => {
    setZooming(true);
    setMaxWidth(Math.max(maxWidth - 100, 400));

    setTimeout(() => {
      setZooming(false);
    }, 300);
  };
  const handlePaginationPrevious = () => {
    listRef.current?.scrollToItem(Math.max(pageNumber - 1, 1) - 1, 'start');
  };
  const handlePaginationNext = () => {
    listRef.current?.scrollToItem(Math.min(pageNumber + 1, pdf?.numPages || 1) - 1, 'start');
  };
  const handlePaginationJump = (pageNumber: number) => {
    listRef.current?.scrollToItem(pageNumber - 1, 'start');
  };

  function getPageHeight(pageIndex: number) {
    if (!pageViewports) {
      throw new Error('getPageHeight() called too early');
    }

    const pageViewport = pageViewports[pageIndex];
    const actualHeight = (width! / pageViewport.width) * pageViewport.height * scale;

    return actualHeight;
  }

  const Row = useMemo(() => {
    // eslint-disable-next-line react/display-name
    return ({ index, style }: { index: number; style: CSSProperties }) => {
      return (
        <div style={style}>
          <Page
            className="flex justify-center"
            pageIndex={index}
            width={width}
            scale={scale}
            renderAnnotationLayer={false}
            onLoadSuccess={() => setZooming(false)}
          />
        </div>
      );
    };
  }, [width, scale]);

  /**
   * React-Window cannot get item size using async getter, therefore we need to
   * calculate them ahead of time.
   */
  useEffect(() => {
    setPageViewports(null);

    if (!pdf) {
      return;
    }

    (async () => {
      const nextPageViewports = await Promise.all(
        Array.from(new Array(pdf.numPages)).map(async (_, i) => {
          const page = await pdf.getPage(i + 1);
          return page.getViewport({ scale: 1 });
        }),
      );

      setPageViewports(nextPageViewports);
    })();
  }, [pdf]);

  return (
    <div
      className="px-4 mx-auto"
      style={{
        maxWidth: `${maxWidth}px`,
      }}
    >
      <div
        ref={ref}
        className={cn(
          'relative h-[calc(100vh-80px)] overflow-x-auto overflow-y-hidden border border-muted shadow-sm',
          className,
        )}
      >
        <Document
          // @see https://github.com/wojtekmaj/react-pdf/issues/974#issuecomment-2671515611
          key={url}
          file={url}
          options={options}
          loading={
            <div className="absolute top-0 flex items-center justify-center w-full h-full bg-card">
              <SimpleLoading />
            </div>
          }
          error={t('failedToLoad')}
          noData={t('noData')}
          onLoadSuccess={onDocumentLoadSuccess}
          onItemClick={onItemClick}
        >
          {/* <Outline /> */}
          {pdf && pageViewports && width && height && (
            <List
              ref={listRef}
              width={width}
              height={height}
              estimatedItemSize={width * 1.5}
              itemCount={pdf.numPages}
              itemSize={getPageHeight}
              onScroll={({ scrollOffset }) => {
                setPageNumber(Math.round(scrollOffset / getPageHeight(0)) + 1);
              }}
            >
              {Row}
            </List>
          )}
        </Document>

        {zooming && (
          <div className="absolute top-0 flex items-center justify-center w-full h-full bg-card">
            <SimpleLoading />
          </div>
        )}
      </div>
      {toolbarEnabled && (
        <div
          className={cn(
            'body absolute bottom-1 left-[50%] mb-safe-bottom flex -translate-x-1/2 items-center justify-center gap-x-2 rounded-full border border-muted bg-card px-3 py-2 shadow-md',
            toolbarClassName,
          )}
        >
          <Button
            iconOnly
            variant="ghost"
            // className="w-6 h-6 rounded-full"
            aria-label="Go to previous page"
            disabled={pageNumber === 1}
            onClick={handlePaginationPrevious}
          >
            <ChevronLeft size={18} />
          </Button>
          <Input
            required
            type="numeric"
            min={1}
            max={pdf?.numPages}
            value={pageNumber}
            step={1}
            // className="inline-block w-8 h-6 p-0 text-center border-none rounded-sm bg-muted"
            onChange={(e) => {
              const pageNumber = Number(e.target.value);
              if (pageNumber >= 1 && pageNumber <= (pdf?.numPages || 0)) {
                setPageNumber(pageNumber);
                handlePaginationJump(pageNumber);
              }
            }}
          />
          <span className="shrink-0 text-caption">
            {'/ '}
            {pdf?.numPages}
          </span>
          <Button
            iconOnly
            variant="ghost"
            // className="w-6 h-6 rounded-full"
            aria-label="Go to next page"
            disabled={pageNumber === pdf?.numPages}
            onClick={handlePaginationNext}
          >
            <ChevronRight size={18} />
          </Button>
          <Separator orientation="vertical" className="h-5 bg-muted" />
          <Button
            iconOnly
            disabled={zooming}
            variant="ghost"
            // className="w-6 h-6 rounded-full"
            onClick={handleZoomOut}
          >
            <ZoomOut size={18} />
          </Button>
          <Button
            iconOnly
            disabled={zooming}
            variant="ghost"
            // className="w-6 h-6 rounded-full"
            onClick={handleZoomIn}
          >
            <ZoomIn size={18} />
          </Button>
        </div>
      )}
    </div>
  );
}
