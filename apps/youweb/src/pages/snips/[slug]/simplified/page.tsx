import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { callHTTP } from '@/utils/callHTTP';
import SimpleView from './simple-view';

export default function SnipSimplifiedPage() {
  const { slug } = useParams<{ slug: string }>();
  const [snip, setSnip] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSnip = async () => {
      if (!slug) return;

      try {
        const response = await callHTTP('/api/v1/snip/getSnip', {
          method: 'POST',
          body: {
            id: slug,
          },
        });
        setSnip(response.data);
      } catch (error) {
        console.error('Error fetching snip:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSnip();
  }, [slug]);

  if (loading) {
    return <SimpleLoading className="h-[100vh]" />;
  }

  if (!snip) {
    return <div>Snip not found</div>;
  }

  return <SimpleView snip={snip} />;
}
