'use client';

import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { TopBanner } from '@repo/ui/components/custom/top-banner';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { useLocation, useNavigation } from 'react-router-dom';
import { SidebarContainer } from '@/components/sidebar';
import { boardCreatorSuccessBoardIdAtom } from '@/hooks/board-creator/useBoardCreator';
import { useFavorite } from '@/hooks/useFavorite';
import { newBoardBackgroundAtom } from '@/hooks/useLayout';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { isSidebarEmbedModeAtom, isSidebarOpenAtom } from '@/hooks/useSidebar';
import { cn } from '@/utils/utils';
import { Background } from './background';

export default function Main({ children }: { children: React.ReactNode }) {
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const newBoardBackground = useAtomValue(newBoardBackgroundAtom);
  const [_boardCreatorSuccessBoardId, _setBoardCreatorSuccessBoardIdd] = useAtom(
    boardCreatorSuccessBoardIdAtom,
  );

  const isMobileSimplifiedPage = useIsMobileSimplifiedPage();
  const location = useLocation();

  const { refreshFavorites } = useFavorite();

  const isEmbedMode = useAtomValue(isSidebarEmbedModeAtom);
  const setIsSidebarOpen = useSetAtom(isSidebarOpenAtom);

  let isNoWhiteContainerPage = useIsNoWhiteContainerPage() || newBoardBackground;

  // 思来想去，只能这么写了。只处理 new board 刚创建好时的临时态
  if (_boardCreatorSuccessBoardId && location.pathname === '/boards/new') {
    isNoWhiteContainerPage = true;
  }

  useEffect(() => {
    if (location.pathname === '/boards/new') {
      _setBoardCreatorSuccessBoardIdd(null);
    }
  }, [location.pathname]);

  useEffect(() => {
    refreshFavorites();
  }, []);

  if (isMobileSimplifiedPage) {
    return (
      <>
        <Helmet>
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1, viewport-fit=cover, user-scalable=no"
          />
          <style type="text/css">{`
            body {
              background-color: transparent !important;
            }
          `}</style>
        </Helmet>
        <div className="relative w-full h-full overflow-x-hidden overflow-y-auto overscroll-contain bg-card">
          {children}
          <div
            style={{
              paddingBottom: 'env(safe-area-inset-bottom)',
            }}
          />
        </div>
      </>
    );
  }

  return (
    <main
      className={cn(
        'relative min-w-0 flex-1 overflow-hidden py-3 transition-[margin-left] duration-300 ease-in-out',
        isDesktop ? 'pr-3' : 'px-3',
        isNoWhiteContainerPage && 'py-0 pr-0',
      )}
      onClick={() => {
        if (isEmbedMode) {
          setIsSidebarOpen(false);
        }
      }}
    >
      <div
        className={cn(
          'relative h-full overflow-hidden',
          !isNoWhiteContainerPage && 'rounded-xl bg-card shadow-sm',
          location.pathname.endsWith('new') && 'overflow-x-hidden',
        )}
      >
        {children}
      </div>
    </main>
  );
}

export function PageLayout({ children }: { children: React.ReactNode }) {
  const location = useLocation();
  const navigation = useNavigation();

  const isLoading = navigation.state === 'loading';

  return (
    <div
      className="relative flex flex-col w-screen h-screen overflow-hidden"
      id="youmind-app-layout"
    >
      <TopBanner
        currentPath={location.pathname}
        uid="xiaohongshu-2025-07"
        className="z-10 flex-shrink-0"
        availablePaths={['/boards', '/boards/new']}
      />
      <div className="flex flex-row flex-grow h-0">
        <Background />
        <SidebarContainer />
        {isLoading ? <SimpleLoading /> : <Main>{children}</Main>}
      </div>
    </div>
  );
}

function useIsNoWhiteContainerPage() {
  const location = useLocation();
  const segments = location.pathname.split('/').filter(Boolean);
  const detailPageType = ['boards', 'thoughts', 'snips', 'chats'];

  if (detailPageType.includes(segments?.[0] as string) && segments?.[1]) {
    if (
      segments?.[0] === 'boards' &&
      (segments?.[1] === 'new' || segments?.[1] === 'board-creator')
    ) {
      return false;
    }
    return true;
  }

  return false;
}

// 移动端用 webview 来渲染 Snip 内容，如果是此类页面，要对 layout 做特殊处理
export function useIsMobileSimplifiedPage() {
  const location = useLocation();

  return (
    (location.pathname.startsWith('/snips') && location.pathname.endsWith('simplified')) ||
    location.pathname.startsWith('/chats')
  );
}
