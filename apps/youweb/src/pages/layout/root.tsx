import { UserWithPreferenceVO } from '@repo/common/types';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { PostHogProvider } from '@repo/ui/lib/posthog/PostHogProvider';
import { Provider, useAtom } from 'jotai';
import { useHydrateAtoms } from 'jotai/utils';
import { type ReactNode } from 'react';
import { Outlet, useNavigation } from 'react-router';
import { HighlightPortalProvider } from '@/components/highlight/HighlightPortalProvider';
import { spaceAtom } from '@/hooks/useSpace';
import { userAtom } from '@/hooks/useUser';
import GlobalPopups from './global-popup';
import { useSetSentryUser } from './hooks/useSetSentryUser';
import { useSetUserTimeZone } from './hooks/useSetUserTimeZone';
import { PageLayout } from './page-layout';

/**
 * 单独注入 jotai，此层以下才可以读写 atom
 * @returns
 */
export const JotaiProvider = ({ children }: { children: ReactNode }) => {
  return <Provider>{children}</Provider>;
};

/**
 * 用户信息注入 jotai
 */
export const UserInjector = ({ children }: { children: ReactNode }) => {
  useHydrateAtoms([
    [userAtom, window.YOUMIND_USER],
    [spaceAtom, window.YOUMIND_USER.space],
  ]);
  return children;
};

/**
 *
 * @returns
 */
function GlobalServicesInjector({ children }: { children: ReactNode }) {
  const [user] = useAtom(userAtom);
  const [space] = useAtom(spaceAtom);
  const userId = user?.id;

  // 设置用户时区
  useSetUserTimeZone(!user?.time_zone);

  // 设置 Sentry 用户信息
  useSetSentryUser();

  const navigation = useNavigation();
  const isLoading = navigation.state === 'loading';

  return (
    <>
      <PostHogProvider user={user as UserWithPreferenceVO}>
        <HighlightPortalProvider>
          {isLoading ? <SimpleLoading /> : <PageLayout> {children}</PageLayout>}
          <GlobalPopups />
        </HighlightPortalProvider>
      </PostHogProvider>
      <script
        src={`https://www.googletagmanager.com/gtag/js?id=${process.env.GOOGLE_ANALYTICS_TAG_ID}`}
        async
      />
      <script id="gtag-init" async>
        {`window.dataLayer = window.dataLayer || [];
              function gtag() {
                window.dataLayer.push(arguments);
              }
              gtag("js", new Date());
              var __user_id = "${userId ?? ''}";
              if (__user_id) {
                gtag("config", "${process.env.GOOGLE_ANALYTICS_TAG_ID}", {
                  user_id: __user_id,
                });
              } else {
                gtag("config", "${process.env.GOOGLE_ANALYTICS_TAG_ID}");
              }
              gtag('set', 'user_properties', {
                'subscription_status': '${space?.subscription?.status ?? ''}'
              });`}
      </script>
      {/* {isDev() && (
          <Script
            crossOrigin="anonymous"
            src="//unpkg.com/react-scan/dist/auto.global.js"
            async
          />
        )} */}
    </>
  );
}

export const RootLayout = () => {
  return (
    <JotaiProvider>
      <UserInjector>
        <GlobalServicesInjector>{<Outlet />}</GlobalServicesInjector>
      </UserInjector>
    </JotaiProvider>
  );
};
