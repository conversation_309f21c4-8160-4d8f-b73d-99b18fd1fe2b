import { MessageRoleEnum } from '@repo/common/types/chat';
import type { CompletionToolBlock } from '@repo/common/types/chat/types';
import { CompletionBlockTypeEnum } from '@repo/common/types/completion';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { useAtom, useAtomValue } from 'jotai';
import { toolDefs } from '@/components/ai-ask/tool';
import { BoardIcon } from '@/components/icon/board';
import { NavigationIcon } from '@/components/icon/navigation';
import { Logo } from '@/components/sidebar/logo-button';
import type { AssistantMessage } from '@/hooks/ask-ai/types';
import { chatAtom } from '@/hooks/ask-ai/useChat';
import {
  BoardCreatePanelStatus,
  boardCreatorAtom,
  boardCreatorSuccessBoardIdAtom,
} from '@/hooks/board-creator/useBoardCreator';
import { boardDetailAtom } from '@/hooks/useBoards';
import { cn } from '@/utils/utils';
import { BoardSidebar } from '../board-workspace/board-siderbar';
import { Workspace } from '../board-workspace/workspace';
import styles from './workspace.module.css';

export function BoardCreatorWorkspace({ loading }: { loading: boolean }) {
  const boardCreator = useAtomValue(boardCreatorAtom);
  const [boardDetail, _setBoardDetail] = useAtom(boardDetailAtom);
  const chat = useAtomValue(chatAtom);

  // boardCreatorSuccessBoardId 有值，说明 board 创建成功并且用户点了 open board，开始动画
  const [boardCreatorSuccessBoardId] = useAtom(boardCreatorSuccessBoardIdAtom);

  const renderWorkSpace = () => {
    if (
      boardCreator.status !== BoardCreatePanelStatus.GENERATING_CONTENT &&
      boardCreator.status !== BoardCreatePanelStatus.SUCCESS
    ) {
      return null;
    }
    return (
      <div className="relative min-h-0 flex-1 overflow-hidden pl-1 pt-1">
        <div
          className={cn(
            'relative mr-[-200px] h-full w-full rounded-tl-[16px] border-l border-t border-muted pl-2 shadow-sm',
            // boardCreatorSuccessBoardId && "bg-card transition-all",
          )}
        >
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '200%',
              height: '100%',
              backgroundImage: "url('https://cdn.gooo.ai/assets/new-background.png')",
              backgroundSize: 'cover',
              backgroundPosition: 'left',
              backgroundRepeat: 'no-repeat',
              opacity: 0.12,
              zIndex: 0,
            }}
          />
          <div className="flex h-[52px] items-center gap-5 pl-[14px]">
            <Logo simplified />
            <NavigationIcon size={20} />
          </div>
          <div className={cn('flex min-h-0 w-[calc(40vw+200px)] flex-row')}>
            {loading || !boardDetail ? <SimpleLoading /> : <WorkSpace loading={loading} />}
          </div>
          <div className="absolute inset-0" />
        </div>
      </div>
    );
  };
  // 最后一个已完成的 tool
  let lastToolBlock: CompletionToolBlock | undefined;
  if (chat?.messages?.length && chat?.messages.length > 0) {
    if (chat?.messages[chat?.messages.length - 1].role === MessageRoleEnum.ASSISTANT) {
      const lastMessage = chat?.messages[chat?.messages.length - 1] as AssistantMessage;
      if (lastMessage.blocks?.length > 0) {
        lastToolBlock = lastMessage.blocks.findLast(
          (block) => block.type === CompletionBlockTypeEnum.TOOL,
        );
      }
    }
  }

  let currentStatus: React.ReactNode = (
    <>
      <BoardIcon size={16} />
      <span>{'Creating board'}</span>
    </>
  );
  if (lastToolBlock) {
    const toolDef = toolDefs.find((tool) => tool.type === lastToolBlock?.tool_name);
    if (toolDef?.getToolTitle?.(lastToolBlock)) {
      currentStatus = toolDef.getToolTitle(lastToolBlock);
    }
  }
  if (boardCreator.status === BoardCreatePanelStatus.SUCCESS) {
    currentStatus = 'Board is ready to open.';
  }

  return (
    <div
      className={cn('flex h-full w-full flex-col', boardCreatorSuccessBoardId && styles.moveBoard)}
    >
      <div
        className={cn(
          'mb-3 flex flex-shrink-0 items-center text-sm font-medium transition-opacity',
          boardCreatorSuccessBoardId && 'opacity-0',
        )}
      >
        {currentStatus}
        {/* {boardCreator.boardId && (
          <a
            href={`/boards/${boardCreator.boardId}`}
            target="_blank"
            rel="noreferrer"
            className="text-blue font-normal underline"
          >
            去真 board 里看看
          </a>
        )} */}
      </div>
      {renderWorkSpace()}
    </div>
  );
}

function WorkSpace({ loading }: { loading: boolean }) {
  return (
    <div className="relative flex min-h-0 flex-1 flex-row">
      {loading ? (
        <SimpleLoading />
      ) : (
        <>
          <BoardSidebar hideHeader />
          <Workspace hideMindStudio hideHeader />
        </>
      )}
    </div>
  );
}
