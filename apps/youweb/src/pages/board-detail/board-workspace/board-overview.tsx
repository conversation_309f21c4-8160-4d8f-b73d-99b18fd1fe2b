import { BoardItemTypeEnum } from '@repo/common/types/board-item';
import type { SnipArticleVO } from '@repo/common/types/snip/app-types';
import { SnipTypeEnum, type SnipVO } from '@repo/common/types/snip/types';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { useAtomValue } from 'jotai';
import { useMemo, useState } from 'react';
import { BoardMasonry } from '@/components/board/board-masonry/masonry';
import { MasonryToolbar } from '@/components/board/board-masonry/masonry-toolbar';
import { useFlattenBoardItemTree } from '@/hooks/useBoardItemTree/useFlattenBoardItemTree';
import { boardDetailAtom } from '@/hooks/useBoards';

export const BoardOverview = ({
  isLoading,
  hideHeader = false,
}: {
  isLoading: boolean;
  hideHeader?: boolean;
}) => {
  const boardDetail = useAtomValue(boardDetailAtom);
  const [selectedTypes, setSelectedTypes] = useState<SnipTypeEnum[]>([]);
  const { flattenedItems } = useFlattenBoardItemTree();

  // 一定要加上 status，不然转存的图片永远无法更新
  const updateKey = flattenedItems
    .map((item) => `${item.id}-${(item.entity as SnipArticleVO)?.status || ''}`)
    .join();

  const boardItems = useMemo(() => {
    return flattenedItems.filter((item) => {
      if (selectedTypes.length === 0) {
        return true;
      }
      switch (item.entity_type) {
        case BoardItemTypeEnum.SNIP:
          return selectedTypes.includes((item.entity as SnipVO).type);
        case BoardItemTypeEnum.THOUGHT:
          return selectedTypes.includes(SnipTypeEnum.THOUGHT);
        default:
          return true;
      }
    });
  }, [updateKey, selectedTypes]);

  if (isLoading) {
    // return <CardListSkeleton cardWidth={SOURCE_WIDTH} />;
    return <SimpleLoading absoluteCenter />;
  }

  const renderBoard = () => {
    if (!boardDetail?.id) {
      return null;
    }
    return (
      <>
        {!hideHeader && (
          <MasonryToolbar
            boardId={boardDetail?.id}
            selectedTypes={selectedTypes}
            onChange={setSelectedTypes}
          />
        )}
        <BoardMasonry boardItems={boardItems} />
      </>
    );
  };

  return <div className="flex h-full flex-col pt-4">{renderBoard()}</div>;
};
