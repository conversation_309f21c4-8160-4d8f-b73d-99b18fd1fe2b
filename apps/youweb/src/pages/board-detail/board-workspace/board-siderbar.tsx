import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { useRef } from 'react';
import { BoardItemDraggableList } from '@/components/board/board-item-draggable-list';
import { ResizeHandle } from '@/components/resizer';
import {
  boardSidebarDraggingAtom,
  getBoardSidebarActiveAtom,
  getBoardSidebarWidthAtom,
  setBoardSidebarWidthAtom,
} from '@/hooks/useBoardState';
import { boardDetailAtom } from '@/hooks/useBoards';
import {
  getMindStudioActiveAtom,
  getMindStudioWidthAtom,
  MIN_MIND_STUDIO_WIDTH,
} from '@/hooks/useMindStudio';
import { cn } from '@/utils/utils';
import BoardSidebarTitle from './board-siderbar-title';

export function BoardSidebar({
  loading = false,
  hideHeader = false,
}: {
  loading?: boolean;
  hideHeader?: boolean;
}) {
  const startXRef = useRef(-1);
  const getBoardSidebarWidth = useAtomValue(getBoardSidebarWidthAtom);
  const setBoardSidebarWidth = useSetAtom(setBoardSidebarWidthAtom);
  const getMindStudioActive = useAtomValue(getMindStudioActiveAtom);
  const boardDetail = useAtomValue(boardDetailAtom);
  const getMindStudioWidth = useAtomValue(getMindStudioWidthAtom);
  const [boardSidebarDragging, setBoardSidebarDragging] = useAtom(boardSidebarDraggingAtom);
  const getBoardSidebarActive = useAtomValue(getBoardSidebarActiveAtom);

  if (!boardDetail?.id) {
    return null;
  }

  return (
    <div
      className={cn(
        'relative flex h-full flex-col',
        !getBoardSidebarActive(boardDetail?.id) && !hideHeader && 'mr-3 !w-0 opacity-0',
        boardSidebarDragging ? 'transition-none' : 'transition-all duration-300',
        !hideHeader && 'pt-[50px]',
      )}
      style={
        getBoardSidebarActive(boardDetail?.id) || hideHeader
          ? {
              width: hideHeader
                ? 210
                : getBoardSidebarWidth(boardDetail?.id) > 240
                  ? getBoardSidebarWidth(boardDetail?.id)
                  : 240,
            }
          : { width: 0 }
      }
    >
      <BoardSidebarTitle hideModifyButton={hideHeader} />
      <div className="min-h-0 flex-1">
        {loading ? <SimpleLoading /> : <BoardItemDraggableList />}
      </div>
      {!hideHeader && (
        <ResizeHandle
          onResetWidth={() => {
            setBoardSidebarWidth(boardDetail?.id, 240);
          }}
          className={cn(
            'absolute -right-[8px] top-[56px] z-10 h-[calc(100%-60px)] opacity-0',
            boardSidebarDragging && 'opacity-60',
          )}
          onSizeChange={(deltaX) => {
            // 最小宽度 200
            setBoardSidebarWidth(
              boardDetail?.id,
              Math.min(
                Math.max(240, startXRef.current - deltaX),
                window.innerWidth -
                  (getMindStudioActive(boardDetail?.id) ? getMindStudioWidth(boardDetail?.id) : 0) -
                  MIN_MIND_STUDIO_WIDTH,
              ),
            );
          }}
          handleStartDragging={() => {
            setBoardSidebarDragging(true);
            startXRef.current =
              getBoardSidebarWidth(boardDetail?.id) > 240
                ? getBoardSidebarWidth(boardDetail?.id)
                : 240;
          }}
          handleStopDragging={() => {
            setBoardSidebarDragging(false);
            startXRef.current = -1;
          }}
        />
      )}
    </div>
  );
}
