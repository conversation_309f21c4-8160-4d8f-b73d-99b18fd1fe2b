'use client';

import { useIsSmallScreen } from '@repo/ui/hooks/useIsSmallScreen';
import useEmblaCarousel from 'embla-carousel-react';
import Link from 'next/link';
import { parseRichTextToString } from '@/cms/cms-util';
import { Media } from '@/cms/components/Media';
import { CollectionResponse, Post } from '@/cms/types';

// 基础的模糊图像placeholder
const placeholderBlur =
  'data:image/png;base64,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';

export default function Posts(props: { posts: CollectionResponse<Post> }) {
  const { posts } = props;
  const [emblaRef] = useEmblaCarousel({
    loop: true,
    align: 'start',
  });
  const { isSmallScreen } = useIsSmallScreen();

  return (
    <div className="mx-auto mt-[120px] w-full max-w-7xl px-4 md:mt-[200px]">
      <h2 className="mb-[60px] text-center font-sans-title text-3xl font-bold md:text-4xl lg:text-[42px]">
        Learn more about YouMind
      </h2>
      {isSmallScreen ? (
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex">
            {posts.docs.map((post) => (
              <div key={post.id} className="pr-4">
                <PostCard post={post} />
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {posts.docs.map((post) => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>
      )}
    </div>
  );
}

function PostCard({ post }: { post: Post }) {
  const { title, slug, heroImage, meta, content, categories } = post;

  // 处理图片路径
  const imageUrl =
    heroImage && typeof heroImage === 'object' && heroImage.url
      ? heroImage.url
      : meta?.image && typeof meta.image === 'object' && meta.image.url
        ? meta.image.url
        : null;

  // 处理描述
  const description = parseRichTextToString(content) || meta?.description;

  // 获取第一个分类的标题
  const categoryTitle =
    categories?.[0] && typeof categories[0] !== 'number' ? categories[0].title : null;

  return (
    <Link href={`/blog/${slug}`}>
      <div className="group w-[306px] overflow-hidden transition-all md:w-[340px] lg:w-[376px]">
        {/* 图片容器 */}
        <div className="relative aspect-[4/3] w-full overflow-hidden rounded-[12px]">
          <Media
            resource={heroImage}
            alt={title}
            fill
            imgClassName="object-cover transition-transform duration-300 group-hover:scale-105"
          />
        </div>

        {/* 内容区 */}
        <div className="py-4">
          {categoryTitle && (
            <div className="mb-2">
              <span className="text-sm font-medium text-caption">{categoryTitle}</span>
            </div>
          )}
          <h3 className="mb-2 line-clamp-2 text-xl font-semibold">{title}</h3>
          <p className="line-clamp-3 text-base text-muted-foreground">{description}</p>
        </div>
      </div>
    </Link>
  );
}
