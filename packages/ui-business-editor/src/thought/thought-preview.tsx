import type { Thought } from '@repo/common/types/thought/types';
import { TitleTypeEnum } from '@repo/common/types/thought/types';
import { base64ToJSON, DIFF_CHANGE_TYPE, DiffTransformUtils, ViewMode } from '@repo/editor-common';
import type { Editor } from '@tiptap/core';
import { useRef, useState } from 'react';
import { EditorExtensionOptions } from '../common/type';
import { ThoughtNavigation } from './navigation/thought-navigation';
import { ThoughtNavigationMobile } from './navigation/thought-navigation-mobile';
import type { ExtensionInitContent } from './thought-editor';
import { ThoughtEditor } from './thought-editor';
import { getThoughtPreviewExtension } from './thought-preview-extension';
import { isMobile } from './utils';

export interface ThoughtPreviewProps {
  id?: string;
  thought: Thought;
  clearDiff?: boolean;
  extensionsOptions?: EditorExtensionOptions;
  showNavigation?: boolean;
}

export const ThoughtPreview = function ThoughtPreview({
  thought,
  clearDiff = true,
  extensionsOptions,
  id = 'shared-thoughts',
  showNavigation,
}: ThoughtPreviewProps) {
  const [editor, setEditor] = useState<Editor | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const diffTransformUtils = new DiffTransformUtils();

  const renderNavigation = () => {
    if (!showNavigation || !editor) return;
    if (isMobile()) {
      return <ThoughtNavigationMobile editor={editor} />;
    }
    return <ThoughtNavigation editor={editor} />;
  };

  const shouldShowTitle = thought.title_type !== TitleTypeEnum.DEFAULT;
  const displayTitle = shouldShowTitle ? thought.title : '';

  // 这里 overflow-visible 是因为选中节点（比如 audio）后，outline 需要超出父元素的边界
  return (
    <div ref={containerRef} className="h-full overflow-visible">
      <div
        className="mb-4 p-0 w-full h-auto text-2xl bg-transparent border-none outline-none font-[600] min-h-10 text-[rgba(0,0,0,0.88)] placeholder:text-[rgba(0,0,0,0.24)] empty:before:text-[rgba(0,0,0,0.24)] empty:before:content-[attr(data-placeholder)]"
        data-placeholder="New thought"
        suppressContentEditableWarning={true}
      >
        {displayTitle}
      </div>
      <ThoughtEditor
        id={id}
        onReady={(params) => {
          setEditor(params.editor);
        }}
        editorOptions={{
          editable: false,
          editorProps: {
            attributes: {
              tabindex: '0',
            },
          },
        }}
        storeOptions={{
          localIndexDB: false,
        }}
        extensions={(context: ExtensionInitContent) =>
          getThoughtPreviewExtension({
            initContent: context,
            options: {
              ...extensionsOptions,
              diffBlockOptions: {
                DiffBlockManageEnable: false,
              },
              characterCountOptions: {
                limit: null,
              },
              mermaidOptions: {
                mode: ViewMode.Preview,
              },
            },
          })
        }
        content={
          clearDiff
            ? diffTransformUtils
                .extractContentFromBase64({
                  base64Content: thought.content.raw,
                  extractType: DIFF_CHANGE_TYPE.REMOVED,
                })
                .toJSON()
            : thought.content.raw
        }
      />
      {renderNavigation()}
    </div>
  );
};
