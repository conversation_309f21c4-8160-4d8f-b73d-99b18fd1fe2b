import { useLayoutEffect, useState } from 'react';

// 小屏幕断点常量
const BREAKPOINT = 768;

export const isMobile = () => {
  if (typeof window === 'undefined') {
    return false;
  }
  return window.innerWidth < BREAKPOINT;
};

/**
 * 客户端判断是否是小屏幕
 */
export const useIsSmallScreen = () => {
  // 使用函数式初始化，在客户端立即检测，避免闪烁
  const [isSmallScreenState, setIsSmallScreenState] = useState<boolean | undefined>(() => {
    // 服务端渲染时返回 undefined
    if (typeof window === 'undefined') {
      return undefined;
    }
    // 客户端立即检测
    return window.innerWidth < BREAKPOINT;
  });

  useLayoutEffect(() => {
    // 使用 matchMedia API，性能更好
    const mql = window.matchMedia(`(max-width: ${BREAKPOINT - 1}px)`);

    // 更新移动端状态的函数
    const onChange = () => {
      setIsSmallScreenState(window.innerWidth < BREAKPOINT);
    };

    // 监听媒体查询变化，比 resize 事件更精确和高效
    mql.addEventListener('change', onChange);

    // 确保状态是最新的（处理竞态条件）
    setIsSmallScreenState(window.innerWidth < BREAKPOINT);

    // 清理函数，移除事件监听器
    return () => mql.removeEventListener('change', onChange);
  }, []);

  // 返回布尔值，在 SSR 时返回 false 作为默认值
  return { isSmallScreen: isSmallScreenState ?? false };
};
