import { Blurhash } from '@repo/ui/components/custom/blurhash';
import { ImageToolbar, type ImageToolbarProps } from '@repo/ui/components/custom/image-toolbar';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { useIsSmallScreen } from '@repo/ui/hooks/useIsSmallScreen';
import { cn } from '@repo/ui/lib/utils';
import { Image as AntdImage } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';

const { PreviewGroup } = AntdImage;

interface ImagePreviewProps extends ImageToolbarProps {
  src: string;
  showLoadingOnError?: boolean;
  alt?: string;
  width?: number | string;
  height?: number | string;
  quality?: 'low' | 'medium' | 'high' | 'auto';
  imageList?: string[];
  currentIndex?: number;
  rounded?: boolean;
  blurhash?: string;
  maxWidth?: string;
  className?: string;
  containerClassName?: string;
  isNewBoard?: boolean;
  onImageEdit?: (data: {
    image_url: string;
    original_image_url: string;
    blurhash: string;
    width: number;
    height: number;
    prompt: string;
  }) => Promise<void>;
  editRender?: (props: { src: string; alt: string }) => React.ReactNode;
  isSaving?: boolean;
  onInsert?: (e: React.MouseEvent) => void;
  onSave?: (e: React.MouseEvent) => void;
  insertToThoughtEnabled?: boolean;
  saveEnabled?: boolean;
  editEnabled?: boolean;
  // 编辑状态控制回调
  onEditStart?: () => void;
  onEditEnd?: () => void;
  isEditing?: boolean;
  isProcessing?: boolean;
}

export const ImagePreview = ({
  src,
  alt = '',
  width,
  height,
  quality,
  className,
  imageList,
  currentIndex = 0,
  showLoadingOnError = false,
  rounded = true,
  blurhash,
  containerClassName,
  isNewBoard = false,
  onImageEdit,
  editRender,
  isSaving = false,
  onInsert,
  onSave,
  insertToThoughtEnabled = false,
  saveEnabled = false,
  editEnabled = false,
  onEditStart,
  onEditEnd,
  isEditing = false,
  isProcessing = false,
  ...rest
}: ImagePreviewProps) => {
  // 基础状态
  const [isLoading, setIsLoading] = useState(true);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [internalCurrentIndex, setInternalCurrentIndex] = useState(currentIndex);

  // DOM引用
  const imgRef = useRef<HTMLImageElement | null>(null);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);
  const doubleClickTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { isSmallScreen } = useIsSmallScreen();

  // 图片加载
  useEffect(() => {
    if (!src) return;

    const ImageElement = new Image();
    ImageElement.src = src;

    const handleLoad = () => {
      setIsLoading(false);
    };

    const handleError = () => {
      setIsLoading(false);
    };

    ImageElement.onload = handleLoad;
    ImageElement.onerror = handleError;

    return () => {
      if (ImageElement) {
        ImageElement.onload = null;
        ImageElement.onerror = null;
      }
    };
  }, [src]);

  // 同步外部 currentIndex 变化到内部状态
  useEffect(() => {
    setInternalCurrentIndex(currentIndex);
  }, [currentIndex]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (doubleClickTimeoutRef.current) {
        clearTimeout(doubleClickTimeoutRef.current);
        doubleClickTimeoutRef.current = null;
      }
    };
  }, []);

  // 最大化预览图片
  const onMaximizeImage = () => {
    setPreviewVisible(true);
  };

  // 处理点击事件，实现单击放大
  const handleClick = () => {
    // 编辑模式下不响应点击事件
    if (isEditing) return;
    // 直接触发放大功能，不需要检测双击
    onMaximizeImage();
  };

  const handleEdit = () => {
    onEditStart?.();
  };

  // 渲染图片占位符
  const renderImagePlaceholder = () => {
    // 只有当 width 和 height 都是 number 时才计算 aspectRatio
    const aspectRatio =
      width && height && typeof width === 'number' && typeof height === 'number'
        ? width / height
        : undefined;

    // 处理 maxWidth，如果是数字则添加 px，否则直接使用（可能是 '100%'）
    const maxWidthStyle = typeof width === 'number' ? `min(${width}px, 100%)` : width || '100%';

    // 处理 minHeight，如果是数字则使用，否则使用默认值
    const minHeightStyle = typeof height === 'number' ? height : 120;

    return blurhash ? (
      <Blurhash
        metadata={{
          width: (width as number) || 0,
          height: (height as number) || 0,
          blurhash,
        }}
      />
    ) : (
      <div className="relative flex flex-col items-center justify-center w-full">
        <div
          className={cn('node-image-container relative overflow-hidden', rounded && 'rounded-lg')}
          style={{
            aspectRatio: aspectRatio,
            maxWidth: maxWidthStyle,
            minHeight: minHeightStyle,
            width: width || '100%', // width 可以是数字或字符串
          }}
        >
          <div className="w-full h-full bg-gray-100">
            <div className="absolute flex h-full min-h-[120px] w-full flex-col items-center justify-center gap-3">
              <div className="dot-pulse" />
            </div>
          </div>
        </div>
      </div>
    );
  };

  const shouldImageAnonymous = useMemo(() => {
    // 微信图片
    if (src.includes('mmbiz.qpic.cn')) {
      return true;
    }
    return false;
  }, [src]);

  // 如果图片正在加载，显示占位符
  if (isLoading) {
    return renderImagePlaceholder();
  }

  // 渲染图片组件
  return (
    <div className={cn('relative flex w-full justify-center', containerClassName)}>
      <div
        className={cn(
          'node-image-container group relative flex items-center justify-center',
          rounded && 'rounded-lg',
        )}
        style={{
          width: width || '100%', // width 可以是数字或字符串
          // 同样，只在宽高都为数字时计算
          aspectRatio:
            width && height && typeof width === 'number' && typeof height === 'number'
              ? width / height
              : undefined,
        }}
        onClick={handleClick}
        ref={imageContainerRef}
      >
        {/* 使用标准的 img 标签显示图片，而不是通过 DOM 操作 */}
        <img
          src={src}
          alt={alt}
          ref={imgRef}
          className={cn('h-auto w-full', className, !isEditing && 'cursor-zoom-in')}
          draggable={true}
          crossOrigin={shouldImageAnonymous ? 'anonymous' : undefined}
          onError={(e) => {
            if (showLoadingOnError) {
              e.currentTarget.src = 'https://cdn.gooo.ai/assets/skeleton_breath.gif';
            }
          }}
        />

        {/* 图片预览组 */}
        {!isEditing && (
          <PreviewGroup
            items={imageList ?? [src]}
            preview={{
              onVisibleChange: (visible) => {
                setPreviewVisible(visible);
              },
              visible: previewVisible,
              current: internalCurrentIndex,
              onChange: (current) => {
                setInternalCurrentIndex(current);
              },
              rootClassName: 'preview-white-background',
            }}
          />
        )}

        {isEditing &&
          editRender &&
          editRender({
            src,
            alt,
          })}

        {isProcessing && (
          <div className="absolute z-[100] flex h-full w-full items-center justify-center bg-card-snips">
            <SimpleLoading className="w-10 h-10" />
          </div>
        )}

        {/* 图片工具栏 */}
        {isSmallScreen || isEditing ? null : (
          <ImageToolbar
            {...rest}
            src={src}
            type="png"
            alt={alt}
            isSaving={isSaving}
            isNewBoard={isNewBoard}
            insertToThoughtEnabled={insertToThoughtEnabled}
            saveEnabled={saveEnabled}
            editEnabled={editEnabled}
            onCopyLink={(e) => {
              e.stopPropagation();
            }}
            onCopyToClipboard={(e) => {
              e.stopPropagation();
            }}
            onDownload={(e) => {
              e.stopPropagation();
            }}
            onInsert={(e) => {
              e.stopPropagation();
              onInsert?.(e);
            }}
            onSave={(e) => {
              e.stopPropagation();
              onSave?.(e);
            }}
            onEdit={(e) => {
              e.stopPropagation();
              handleEdit();
            }}
          />
        )}
      </div>
    </div>
  );
};
