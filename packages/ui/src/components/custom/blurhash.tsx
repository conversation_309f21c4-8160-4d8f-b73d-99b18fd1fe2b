import type { SnipVO } from '@repo/common/types/snip/app-types';
import { cn } from '@repo/ui/lib/utils';
import { Blurhash as Bh } from 'react-blurhash';

interface BlurhashProps extends React.HTMLAttributes<HTMLDivElement> {
  snip?: SnipVO;
  metadata?: ImageMetadata;
}

export interface ImageMetadata {
  width: number;
  height: number;
  blurhash: string;
}

export const Blurhash: React.FC<BlurhashProps> = ({ snip, metadata, className }) => {
  const { extra } = snip || {};

  if (extra) {
    try {
      metadata = JSON.parse(extra).hero_image_metadata;
    } catch {
      // ignore
    }
  }

  return metadata?.blurhash ? (
    <div
      className={cn('center size-full max-w-full', className)}
      style={{
        aspectRatio:
          metadata?.height && metadata?.width ? metadata.width / metadata.height : undefined,
      }}
    >
      <Bh hash={metadata?.blurhash} resolutionX={32} resolutionY={32} className="!size-full" />
    </div>
  ) : (
    <></>
  );
};
