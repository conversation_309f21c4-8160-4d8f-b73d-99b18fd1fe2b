import { cn } from '@repo/ui/lib/utils';
import styles from './index.module.css';

interface SimpleLoadingProps {
  fullScreen?: boolean;
  className?: string;
  absoluteCenter?: boolean;
  variant?: 'dot-pulse' | 'dot-falling' | 'dot-stretching';
}

export const SimpleLoading = ({
  fullScreen,
  className,
  absoluteCenter,
  variant = 'dot-pulse',
}: SimpleLoadingProps) => {
  return (
    <div
      className={cn(
        'flex h-full min-h-[120px] w-full flex-col items-center justify-center gap-3',
        fullScreen && 'h-screen w-screen',
        className,
        absoluteCenter && 'absolute inset-0',
        styles.container,
      )}
    >
      <div className={styles[variant]}></div>
    </div>
  );
};

// 他们不喜欢，我还挺喜欢的
// export const SimpleLoading = ({
//   fullScreen,
//   className,
//   absoluteCenter,
// }: SimpleLoadingProps) => {
//   return (
//     <div
//       className={cn(
//         "flex h-full min-h-[120px] w-full flex-col items-center justify-center gap-3",
//         fullScreen && "h-screen w-screen",
//         className,
//         absoluteCenter && "absolute inset-0",
//       )}
//     >
//       <div className={styles.loading}>
//         <div className={styles.line}></div>
//         <div className={styles.line}></div>
//         <div className={styles.line}></div>
//       </div>
//       {/* <span className="text-caption-foreground">Loading...</span> */}
//     </div>
//   );
// };
